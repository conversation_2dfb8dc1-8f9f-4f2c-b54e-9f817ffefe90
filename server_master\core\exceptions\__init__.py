"""
异常处理模块
"""
from .error_codes import <PERSON>rror<PERSON><PERSON>, ErrorCategory, CommonErrors, ErrorCodeRegistry
from .exceptions import (
    StandardException,
    BusinessException,
    SystemException,
    ValidationException,
    AuthenticationException,
    AuthorizationException,
    ResourceNotFoundException,
    ResourceAlreadyExistsException,
    DatabaseException,
    CacheException,
    ExternalServiceException,
    RateLimitException,
    ExceptionFactory
)
from .handler import ExceptionHandler, setup_exception_handlers, ExceptionContext, handle_exceptions

__all__ = [
    # 错误码
    'ErrorCode',
    'ErrorCategory',
    'CommonErrors',
    'ErrorCodeRegistry',
    
    # 异常类
    'StandardException',
    'BusinessException',
    'SystemException',
    'ValidationException',
    'AuthenticationException',
    'AuthorizationException',
    'ResourceNotFoundException',
    'ResourceAlreadyExistsException',
    'DatabaseException',
    'CacheException',
    'ExternalServiceException',
    'RateLimitException',
    'ExceptionFactory',
    
    # 处理器
    'ExceptionHandler',
    'setup_exception_handlers',
    'ExceptionContext',
    'handle_exceptions'
]
