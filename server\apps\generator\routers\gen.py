from datetime import datetime
from fastapi import APIRouter, Depends, Query, Request
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config import settings, GeneratorConfig
from core.database.database_manager import get_db
from apps.admin.annotation.log_annotation import Log
from apps.admin.aspect.interface_auth import CheckRoleInterfaceAuth, CheckUserInterfaceAuth
from apps.admin.services.login import LoginService
from apps.admin.schemas.user import CurrentUserSchema
from apps.generator.schemas.gen import DeleteGenTableSchema, EditGenTableSchema, GenTablePageQuerySchema
from apps.generator.services.gen import GenTableColumnService, GenTableService
from common.common import bytes2file_response
from core.logging.manager import logger
from common.page import PageResponseModel
from common.response import ResponseService


router = APIRouter(prefix='/tool/gen', dependencies=[Depends(LoginService.get_current_user)])


@router.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:list'))]
)
async def get_gen_table_list(
    request: Request,
    gen_page_query: GenTablePageQuerySchema = Depends(GenTablePageQuerySchema.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    gen_page_query_result = await GenTableService.get_gen_table_list_services(query_db, gen_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseService.success(model_content=gen_page_query_result)


@router.get(
    '/db/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:list'))]
)
async def get_gen_db_table_list(
    request: Request,
    gen_page_query: GenTablePageQuerySchema = Depends(GenTablePageQuerySchema.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    gen_page_query_result = await GenTableService.get_gen_db_table_list_services(query_db, gen_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseService.success(model_content=gen_page_query_result)


@router.post('/importTable', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:import'))])
@Log(title='代码生成', business_type=BusinessType.IMPORT)
async def import_gen_table(
    request: Request,
    tables: str = Query(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserSchema = Depends(LoginService.get_current_user),
):
    table_names = tables.split(',') if tables else []
    add_gen_table_list = await GenTableService.get_gen_db_table_list_by_name_services(query_db, table_names)
    add_gen_table_result = await GenTableService.import_gen_table_services(query_db, add_gen_table_list, current_user)
    logger.info(add_gen_table_result.message)

    return ResponseService.success(msg=add_gen_table_result.message)


@router.put('', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:edit'))])
@ValidateFields(validate_model='edit_gen_table')
@Log(title='代码生成', business_type=BusinessType.UPDATE)
async def edit_gen_table(
    request: Request,
    edit_gen_table: EditGenTableSchema,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserSchema = Depends(LoginService.get_current_user),
):
    edit_gen_table.update_by = current_user.user.user_name
    edit_gen_table.update_time = datetime.now()
    await GenTableService.validate_edit(edit_gen_table)
    edit_gen_result = await GenTableService.edit_gen_table_services(query_db, edit_gen_table)
    logger.info(edit_gen_result.message)

    return ResponseService.success(msg=edit_gen_result.message)


@router.delete('/{table_ids}', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:remove'))])
@Log(title='代码生成', business_type=BusinessType.DELETE)
async def delete_gen_table(request: Request, table_ids: str, query_db: AsyncSession = Depends(get_db)):
    delete_gen_table = DeleteGenTableSchema(tableIds=table_ids)
    delete_gen_table_result = await GenTableService.delete_gen_table_services(query_db, delete_gen_table)
    logger.info(delete_gen_table_result.message)

    return ResponseService.success(msg=delete_gen_table_result.message)


@router.post('/createTable', dependencies=[Depends(CheckRoleInterfaceAuth('admin'))])
@Log(title='创建表', business_type=BusinessType.OTHER)
async def create_table(
    request: Request,
    sql: str = Query(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserSchema = Depends(LoginService.get_current_user),
):
    create_table_result = await GenTableService.create_table_services(query_db, sql, current_user)
    logger.info(create_table_result.message)

    return ResponseService.success(msg=create_table_result.message)


@router.get('/batchGenCode', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:code'))])
@Log(title='代码生成', business_type=BusinessType.GENCODE)
async def batch_gen_code(request: Request, tables: str = Query(), query_db: AsyncSession = Depends(get_db)):
    table_names = tables.split(',') if tables else []
    batch_gen_code_result = await GenTableService.batch_gen_code_services(query_db, table_names)
    logger.info('生成代码成功')

    return ResponseService.streaming(data=bytes2file_response(batch_gen_code_result))


@router.get('/genCode/{table_name}', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:code'))])
@Log(title='代码生成', business_type=BusinessType.GENCODE)
async def gen_code_local(request: Request, table_name: str, query_db: AsyncSession = Depends(get_db)):
    if not GeneratorConfig.allow_overwrite:
        logger.error('【系统预设】不允许生成文件覆盖到本地')
        return ResponseService.error('【系统预设】不允许生成文件覆盖到本地')
    gen_code_local_result = await GenTableService.generate_code_services(query_db, table_name)
    logger.info(gen_code_local_result.message)

    return ResponseService.success(msg=gen_code_local_result.message)


@router.get('/{table_id}', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:query'))])
async def query_detail_gen_table(request: Request, table_id: int, query_db: AsyncSession = Depends(get_db)):
    gen_table = await GenTableService.get_gen_table_by_id_services(query_db, table_id)
    gen_tables = await GenTableService.get_gen_table_all_services(query_db)
    gen_columns = await GenTableColumnService.get_gen_table_column_list_by_table_id_services(query_db, table_id)
    gen_table_detail_result = dict(info=gen_table, rows=gen_columns, tables=gen_tables)
    logger.info(f'获取table_id为{table_id}的信息成功')

    return ResponseService.success(data=gen_table_detail_result)


@router.get('/preview/{table_id}', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:preview'))])
async def preview_code(request: Request, table_id: int, query_db: AsyncSession = Depends(get_db)):
    preview_code_result = await GenTableService.preview_code_services(query_db, table_id)
    logger.info('获取预览代码成功')

    return ResponseService.success(data=preview_code_result)


@router.get('/synchDb/{table_name}', dependencies=[Depends(CheckUserInterfaceAuth('tool:gen:edit'))])
@Log(title='代码生成', business_type=BusinessType.UPDATE)
async def sync_db(request: Request, table_name: str, query_db: AsyncSession = Depends(get_db)):
    sync_db_result = await GenTableService.sync_db_services(query_db, table_name)
    logger.info(sync_db_result.message)

    return ResponseService.success(data=sync_db_result.message)
