# 开发环境Dockerfile
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    APP_ENV=development

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装开发工具
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-cov \
    black \
    isort \
    flake8 \
    mypy

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs storage/uploads storage/downloads storage/generated assets/font

# 暴露端口
EXPOSE 9099

# 启动命令（开发模式，支持热重载）
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9099", "--reload"]
