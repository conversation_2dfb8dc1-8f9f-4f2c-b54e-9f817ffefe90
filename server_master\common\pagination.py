"""
分页工具模块
"""
import math
from typing import List, Dict, Any, Optional, TypeVar, Generic
from pydantic import BaseModel, Field
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

T = TypeVar('T')


class PageResponseModel(BaseModel, Generic[T]):
    """分页响应模型"""
    
    rows: List[T] = Field(description="数据列表")
    total: int = Field(description="总记录数")
    page_num: int = Field(description="当前页码")
    page_size: int = Field(description="每页大小")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")


class PaginationUtil:
    """分页工具类"""

    @staticmethod
    async def paginate(
        session: AsyncSession,
        query: select,
        page_num: int = 1,
        page_size: int = 10,
        count_query: Optional[select] = None
    ) -> Dict[str, Any]:
        """
        执行分页查询
        
        Args:
            session: 数据库会话
            query: 查询语句
            page_num: 页码（从1开始）
            page_size: 每页大小
            count_query: 计数查询语句，如果不提供则自动生成
            
        Returns:
            分页结果字典
        """
        # 验证参数
        page_num = max(1, page_num)
        page_size = max(1, min(page_size, 1000))  # 限制最大页面大小
        
        # 获取总记录数
        if count_query is not None:
            total_result = await session.execute(count_query)
            total = total_result.scalar()
        else:
            # 自动生成计数查询
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await session.execute(count_query)
            total = total_result.scalar()
        
        # 计算分页信息
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        has_next = page_num < total_pages
        has_prev = page_num > 1
        
        # 执行分页查询
        offset = (page_num - 1) * page_size
        paginated_query = query.offset(offset).limit(page_size)
        result = await session.execute(paginated_query)
        rows = result.scalars().all()
        
        return {
            'rows': rows,
            'total': total,
            'page_num': page_num,
            'page_size': page_size,
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev
        }

    @staticmethod
    async def cursor_paginate(
        session: AsyncSession,
        query: select,
        cursor_field: str,
        cursor: Optional[Any] = None,
        limit: int = 20,
        order_desc: bool = True
    ) -> Dict[str, Any]:
        """
        基于游标的分页查询（适用于大数据量场景）
        
        Args:
            session: 数据库会话
            query: 查询语句
            cursor_field: 游标字段名
            cursor: 游标值
            limit: 限制数量
            order_desc: 是否降序排列
            
        Returns:
            分页结果字典
        """
        # 验证参数
        limit = max(1, min(limit, 1000))
        
        # 构建查询条件
        if cursor is not None:
            # 获取模型类
            model_class = query.column_descriptions[0]['entity']
            cursor_column = getattr(model_class, cursor_field)
            
            if order_desc:
                query = query.where(cursor_column < cursor)
                query = query.order_by(cursor_column.desc())
            else:
                query = query.where(cursor_column > cursor)
                query = query.order_by(cursor_column.asc())
        else:
            # 首次查询
            model_class = query.column_descriptions[0]['entity']
            cursor_column = getattr(model_class, cursor_field)
            
            if order_desc:
                query = query.order_by(cursor_column.desc())
            else:
                query = query.order_by(cursor_column.asc())
        
        # 多查询一条判断是否有下一页
        query = query.limit(limit + 1)
        result = await session.execute(query)
        items = result.scalars().all()
        
        has_next = len(items) > limit
        if has_next:
            items = items[:-1]
        
        # 获取下一个游标
        next_cursor = getattr(items[-1], cursor_field) if items and has_next else None
        
        return {
            'items': items,
            'has_next': has_next,
            'next_cursor': next_cursor,
            'limit': limit
        }

    @staticmethod
    def create_page_response(
        rows: List[T],
        total: int,
        page_num: int,
        page_size: int
    ) -> PageResponseModel[T]:
        """
        创建分页响应对象
        
        Args:
            rows: 数据列表
            total: 总记录数
            page_num: 当前页码
            page_size: 每页大小
            
        Returns:
            分页响应对象
        """
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        has_next = page_num < total_pages
        has_prev = page_num > 1
        
        return PageResponseModel(
            rows=rows,
            total=total,
            page_num=page_num,
            page_size=page_size,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )

    @staticmethod
    def validate_pagination_params(page_num: int, page_size: int) -> tuple[int, int]:
        """
        验证分页参数
        
        Args:
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            验证后的页码和每页大小
        """
        page_num = max(1, page_num)
        page_size = max(1, min(page_size, 1000))
        return page_num, page_size

    @staticmethod
    def calculate_offset(page_num: int, page_size: int) -> int:
        """
        计算偏移量
        
        Args:
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            偏移量
        """
        return (page_num - 1) * page_size
