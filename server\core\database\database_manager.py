"""
数据库管理器模块
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from urllib.parse import quote_plus

from config import settings

from core.logging.manager import logger


# 定义数据库基类，用于继承
class Base(AsyncAttrs, DeclarativeBase):
    pass


class DatabaseManager:
    """数据库管理器，提供数据库连接、会话管理和初始化功能"""

    _async_engine = None
    _sync_engine = None
    _async_session_factory = None
    _sync_session_factory = None

    @classmethod
    def get_async_database_url(cls) -> str:
        """
        获取异步数据库URL

        :return: 异步数据库URL字符串
        """
        db_config = settings.database
        if db_config.type == 'postgresql':
            driver = 'postgresql+asyncpg'
        else:
            driver = 'mysql+asyncmy'

        return (
            f'{driver}://'
            f'{db_config.username}:{quote_plus(db_config.password)}@'
            f'{db_config.host}:{db_config.port}/{db_config.database}'
        )

    @classmethod
    def get_sync_database_url(cls) -> str:
        """
        获取同步数据库URL（用于调度器等场景）

        :return: 同步数据库URL字符串
        """
        db_config = settings.database
        if db_config.type == 'postgresql':
            driver = 'postgresql+psycopg2'
        else:
            driver = 'mysql+pymysql'

        return (
            f'{driver}://'
            f'{db_config.username}:{quote_plus(db_config.password)}@'
            f'{db_config.host}:{db_config.port}/{db_config.database}'
        )

    @classmethod
    def get_async_engine(cls):
        """
        获取异步数据库引擎

        :return: 异步数据库引擎对象
        """
        if cls._async_engine is None:
            db_config = settings.database
            cls._async_engine = create_async_engine(
                cls.get_async_database_url(),
                echo=db_config.echo,
                max_overflow=db_config.max_overflow,
                pool_size=db_config.pool_size,
                pool_recycle=db_config.pool_recycle,
                pool_timeout=db_config.pool_timeout,
            )
        return cls._async_engine

    @classmethod
    def get_sync_engine(cls):
        """
        获取同步数据库引擎

        :return: 同步数据库引擎对象
        """
        if cls._sync_engine is None:
            db_config = settings.database
            cls._sync_engine = create_engine(
                cls.get_sync_database_url(),
                echo=db_config.echo,
                max_overflow=db_config.max_overflow,
                pool_size=db_config.pool_size,
                pool_recycle=db_config.pool_recycle,
                pool_timeout=db_config.pool_timeout,
            )
        return cls._sync_engine

    @classmethod
    def get_async_session_factory(cls):
        """
        获取异步会话工厂

        :return: 异步会话工厂对象
        """
        if cls._async_session_factory is None:
            cls._async_session_factory = async_sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=cls.get_async_engine()
            )
        return cls._async_session_factory

    @classmethod
    def get_sync_session_factory(cls):
        """
        获取同步会话工厂

        :return: 同步会话工厂对象
        """
        if cls._sync_session_factory is None:
            cls._sync_session_factory = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=cls.get_sync_engine()
            )
        return cls._sync_session_factory

    @classmethod
    async def get_async_session(cls):
        """
        获取异步数据库会话

        :yield: 异步数据库会话对象
        """
        async_session_factory = cls.get_async_session_factory()
        async with async_session_factory() as session:
            yield session

    @classmethod
    async def init_database(cls):
        """
        初始化数据库结构

        :return: 无返回值
        """
        logger.info('开始连接数据库...')
        async_engine = cls.get_async_engine()
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info('数据库连接成功')


# 向后兼容的别名
AsyncSessionLocal = DatabaseManager.get_async_session_factory()
async_engine = DatabaseManager.get_async_engine()
get_db = DatabaseManager.get_async_session