"""
API限流器模块
"""
import time
import asyncio
from typing import Dict, Any, Optional
from functools import wraps
from fastapi import Request, HTTPException, status

from ..logging.structured_logger import structured_logger
from ..exceptions import RateLimitException
from ..cache.cache_manager import CacheManager


class RateLimiter:
    """API限流器"""

    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager

    async def is_allowed(
        self,
        key: str,
        limit: int,
        window: int = 60,
        algorithm: str = 'sliding_window'
    ) -> Dict[str, Any]:
        """检查是否允许请求"""
        if algorithm == 'sliding_window':
            return await self._sliding_window_check(key, limit, window)
        elif algorithm == 'token_bucket':
            return await self._token_bucket_check(key, limit, window)
        elif algorithm == 'fixed_window':
            return await self._fixed_window_check(key, limit, window)
        else:
            raise ValueError(f"不支持的限流算法: {algorithm}")

    async def _sliding_window_check(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """滑动窗口算法"""
        current_time = int(time.time())
        window_start = current_time - window

        try:
            # 使用Redis的有序集合实现滑动窗口
            pipe = self.cache_manager.redis.pipeline()
            
            # 删除窗口外的记录
            pipe.zremrangebyscore(key, 0, window_start)
            # 获取当前窗口内的请求数
            pipe.zcard(key)
            # 添加当前请求
            pipe.zadd(key, {str(current_time): current_time})
            # 设置过期时间
            pipe.expire(key, window)

            results = await pipe.execute()
            current_requests = results[1]

            allowed = current_requests < limit
            remaining = max(0, limit - current_requests - 1) if allowed else 0
            reset_time = current_time + window

            structured_logger.debug(
                f"滑动窗口限流检查",
                key=key,
                current_requests=current_requests,
                limit=limit,
                allowed=allowed
            )

            return {
                'allowed': allowed,
                'limit': limit,
                'remaining': remaining,
                'reset_time': reset_time,
                'retry_after': window if not allowed else 0
            }

        except Exception as e:
            structured_logger.error(f"滑动窗口限流检查失败: {e}")
            # 限流器故障时允许请求通过
            return {
                'allowed': True,
                'limit': limit,
                'remaining': limit - 1,
                'reset_time': current_time + window,
                'retry_after': 0
            }

    async def _token_bucket_check(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """令牌桶算法"""
        current_time = time.time()
        bucket_key = f"bucket:{key}"

        try:
            # 获取桶状态
            bucket_data = await self.cache_manager.get(bucket_key)
            
            if bucket_data:
                last_refill = bucket_data['last_refill']
                tokens = bucket_data['tokens']
            else:
                last_refill = current_time
                tokens = limit

            # 计算需要添加的令牌数
            time_passed = current_time - last_refill
            tokens_to_add = int(time_passed * (limit / window))
            tokens = min(limit, tokens + tokens_to_add)

            # 检查是否有可用令牌
            allowed = tokens > 0
            if allowed:
                tokens -= 1

            # 更新桶状态
            bucket_data = {
                'tokens': tokens,
                'last_refill': current_time
            }
            await self.cache_manager.set(bucket_key, bucket_data, window * 2)

            reset_time = current_time + (limit - tokens) * (window / limit)

            structured_logger.debug(
                f"令牌桶限流检查",
                key=key,
                tokens=tokens,
                limit=limit,
                allowed=allowed
            )

            return {
                'allowed': allowed,
                'limit': limit,
                'remaining': tokens,
                'reset_time': int(reset_time),
                'retry_after': int((window / limit)) if not allowed else 0
            }

        except Exception as e:
            structured_logger.error(f"令牌桶限流检查失败: {e}")
            return {
                'allowed': True,
                'limit': limit,
                'remaining': limit - 1,
                'reset_time': int(current_time + window),
                'retry_after': 0
            }

    async def _fixed_window_check(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """固定窗口算法"""
        current_time = int(time.time())
        window_start = (current_time // window) * window
        window_key = f"{key}:{window_start}"

        try:
            # 获取当前窗口的请求数
            current_requests = await self.cache_manager.get(window_key) or 0
            
            allowed = current_requests < limit
            if allowed:
                # 增加请求计数
                await self.cache_manager.increment(window_key)
                await self.cache_manager.expire(window_key, window)

            remaining = max(0, limit - current_requests - 1) if allowed else 0
            reset_time = window_start + window

            structured_logger.debug(
                f"固定窗口限流检查",
                key=key,
                current_requests=current_requests,
                limit=limit,
                allowed=allowed
            )

            return {
                'allowed': allowed,
                'limit': limit,
                'remaining': remaining,
                'reset_time': reset_time,
                'retry_after': reset_time - current_time if not allowed else 0
            }

        except Exception as e:
            structured_logger.error(f"固定窗口限流检查失败: {e}")
            return {
                'allowed': True,
                'limit': limit,
                'remaining': limit - 1,
                'reset_time': current_time + window,
                'retry_after': 0
            }

    async def get_rate_limit_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取限流信息"""
        try:
            # 尝试从不同的算法获取信息
            bucket_data = await self.cache_manager.get(f"bucket:{key}")
            if bucket_data:
                return {
                    'algorithm': 'token_bucket',
                    'tokens': bucket_data['tokens'],
                    'last_refill': bucket_data['last_refill']
                }

            # 检查滑动窗口
            current_time = int(time.time())
            window_requests = await self.cache_manager.redis.zcard(key)
            if window_requests > 0:
                return {
                    'algorithm': 'sliding_window',
                    'current_requests': window_requests
                }

            return None

        except Exception as e:
            structured_logger.error(f"获取限流信息失败: {e}")
            return None


class RateLimitConfig:
    """限流配置"""

    def __init__(self):
        self.default_limit = 100
        self.default_window = 60
        self.default_algorithm = 'sliding_window'
        self.endpoint_configs = {}

    def set_endpoint_config(
        self, 
        endpoint: str, 
        limit: int, 
        window: int = 60, 
        algorithm: str = 'sliding_window'
    ):
        """设置端点限流配置"""
        self.endpoint_configs[endpoint] = {
            'limit': limit,
            'window': window,
            'algorithm': algorithm
        }

    def get_endpoint_config(self, endpoint: str) -> Dict[str, Any]:
        """获取端点限流配置"""
        return self.endpoint_configs.get(endpoint, {
            'limit': self.default_limit,
            'window': self.default_window,
            'algorithm': self.default_algorithm
        })


# 全局限流配置
rate_limit_config = RateLimitConfig()


def rate_limit(
    limit: int = 100, 
    window: int = 60, 
    algorithm: str = 'sliding_window',
    key_func: Optional[callable] = None
):
    """限流装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # 获取缓存管理器
            cache_manager = CacheManager.get_instance()
            rate_limiter = RateLimiter(cache_manager)

            # 生成限流键
            if key_func:
                rate_key = key_func(request)
            else:
                client_ip = request.client.host
                endpoint = request.url.path
                rate_key = f"rate_limit:{client_ip}:{endpoint}"

            # 检查限流
            result = await rate_limiter.is_allowed(rate_key, limit, window, algorithm)
            
            if not result['allowed']:
                structured_logger.warning(
                    f"请求被限流",
                    key=rate_key,
                    limit=limit,
                    window=window,
                    client_ip=request.client.host,
                    endpoint=request.url.path
                )
                
                raise RateLimitException(
                    limit=limit,
                    window=window,
                    detail=f"请求频率超限，请{result['retry_after']}秒后重试"
                )

            # 添加限流头信息
            response = await func(request, *args, **kwargs)
            
            if hasattr(response, 'headers'):
                response.headers['X-RateLimit-Limit'] = str(result['limit'])
                response.headers['X-RateLimit-Remaining'] = str(result['remaining'])
                response.headers['X-RateLimit-Reset'] = str(result['reset_time'])

            return response

        return wrapper
    return decorator


def ip_rate_limit(limit: int = 100, window: int = 60):
    """基于IP的限流装饰器"""
    def key_func(request: Request) -> str:
        return f"ip_rate_limit:{request.client.host}"
    
    return rate_limit(limit, window, key_func=key_func)


def user_rate_limit(limit: int = 1000, window: int = 60):
    """基于用户的限流装饰器"""
    def key_func(request: Request) -> str:
        # 从请求中获取用户ID（需要先进行认证）
        user_id = getattr(request.state, 'user_id', 'anonymous')
        return f"user_rate_limit:{user_id}"
    
    return rate_limit(limit, window, key_func=key_func)


def endpoint_rate_limit(limit: int = 50, window: int = 60):
    """基于端点的限流装饰器"""
    def key_func(request: Request) -> str:
        return f"endpoint_rate_limit:{request.url.path}"
    
    return rate_limit(limit, window, key_func=key_func)
