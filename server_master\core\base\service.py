"""
基础服务抽象类模块
"""
from abc import ABC, abstractmethod
from typing import Generic, TypeVar, Optional, List, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

T = TypeVar('T')
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)


class BaseService(Generic[T, CreateSchemaType, UpdateSchemaType], ABC):
    """基础服务抽象类"""

    def __init__(self, session: AsyncSession):
        self.session = session

    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> T:
        """创建对象"""
        pass

    @abstractmethod
    async def get_by_id(self, id: Union[int, str]) -> Optional[T]:
        """根据ID获取对象"""
        pass

    @abstractmethod
    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> Optional[T]:
        """更新对象"""
        pass

    @abstractmethod
    async def delete(self, id: Union[int, str]) -> bool:
        """删除对象"""
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        """获取对象列表"""
        pass

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取对象总数"""
        pass

    async def exists(self, id: Union[int, str]) -> bool:
        """检查对象是否存在"""
        obj = await self.get_by_id(id)
        return obj is not None

    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[T]:
        """批量创建对象"""
        results = []
        for obj_in in objs_in:
            result = await self.create(obj_in)
            results.append(result)
        return results

    async def bulk_update(self, updates: List[Dict[str, Any]]) -> List[T]:
        """批量更新对象"""
        results = []
        for update_data in updates:
            obj_id = update_data.pop('id')
            result = await self.update(obj_id, update_data)
            if result:
                results.append(result)
        return results

    async def bulk_delete(self, ids: List[Union[int, str]]) -> int:
        """批量删除对象"""
        deleted_count = 0
        for obj_id in ids:
            if await self.delete(obj_id):
                deleted_count += 1
        return deleted_count


class CRUDService(BaseService[T, CreateSchemaType, UpdateSchemaType]):
    """CRUD服务基类，提供默认的CRUD实现"""

    def __init__(self, session: AsyncSession, repository):
        super().__init__(session)
        self.repository = repository

    async def create(self, obj_in: CreateSchemaType) -> T:
        """创建对象"""
        return await self.repository.create(obj_in)

    async def get_by_id(self, id: Union[int, str]) -> Optional[T]:
        """根据ID获取对象"""
        return await self.repository.get_by_id(id)

    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> Optional[T]:
        """更新对象"""
        return await self.repository.update(id, obj_in)

    async def delete(self, id: Union[int, str]) -> bool:
        """删除对象"""
        return await self.repository.delete(id)

    async def list(self, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        """获取对象列表"""
        return await self.repository.list(skip=skip, limit=limit, filters=filters)

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取对象总数"""
        return await self.repository.count(filters=filters)


class BusinessService(BaseService[T, CreateSchemaType, UpdateSchemaType]):
    """业务服务基类，用于复杂业务逻辑"""

    def __init__(self, session: AsyncSession):
        super().__init__(session)
        self._repositories = {}

    def add_repository(self, name: str, repository):
        """添加仓储"""
        self._repositories[name] = repository

    def get_repository(self, name: str):
        """获取仓储"""
        return self._repositories.get(name)

    async def begin_transaction(self):
        """开始事务"""
        return self.session.begin()

    async def commit_transaction(self):
        """提交事务"""
        await self.session.commit()

    async def rollback_transaction(self):
        """回滚事务"""
        await self.session.rollback()
