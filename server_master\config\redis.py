"""
Redis配置模块
"""
from pydantic import computed_field
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any


class RedisConfig(BaseSettings):
    """Redis配置"""

    # 基础连接配置
    host: str = '127.0.0.1'
    port: int = 6379
    password: Optional[str] = None
    database: int = 0

    # 连接池配置
    max_connections: int = 100
    retry_on_timeout: bool = True
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = {}

    # 集群配置
    cluster_enabled: bool = False
    cluster_nodes: list = []

    # 哨兵配置
    sentinel_enabled: bool = False
    sentinel_hosts: list = []
    sentinel_service_name: str = 'mymaster'

    # SSL配置
    ssl_enabled: bool = False
    ssl_cert_reqs: str = 'required'
    ssl_ca_certs: Optional[str] = None
    ssl_certfile: Optional[str] = None
    ssl_keyfile: Optional[str] = None

    # 性能配置
    decode_responses: bool = True
    encoding: str = 'utf-8'
    health_check_interval: int = 30

    # 缓存配置
    default_ttl: int = 3600  # 默认1小时
    key_prefix: str = 'mxtt:'

    @computed_field
    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        if self.password:
            auth = f":{self.password}@"
        else:
            auth = ""

        protocol = "rediss" if self.ssl_enabled else "redis"
        return f"{protocol}://{auth}{self.host}:{self.port}/{self.database}"

    @computed_field
    @property
    def connection_kwargs(self) -> Dict[str, Any]:
        """Redis连接参数"""
        kwargs = {
            'host': self.host,
            'port': self.port,
            'db': self.database,
            'password': self.password,
            'socket_timeout': self.socket_timeout,
            'socket_connect_timeout': self.socket_connect_timeout,
            'socket_keepalive': self.socket_keepalive,
            'socket_keepalive_options': self.socket_keepalive_options,
            'retry_on_timeout': self.retry_on_timeout,
            'decode_responses': self.decode_responses,
            'encoding': self.encoding,
            'health_check_interval': self.health_check_interval,
        }

        # SSL配置
        if self.ssl_enabled:
            kwargs.update({
                'ssl': True,
                'ssl_cert_reqs': self.ssl_cert_reqs,
                'ssl_ca_certs': self.ssl_ca_certs,
                'ssl_certfile': self.ssl_certfile,
                'ssl_keyfile': self.ssl_keyfile,
            })

        return kwargs

    @computed_field
    @property
    def connection_pool_kwargs(self) -> Dict[str, Any]:
        """Redis连接池参数"""
        return {
            'max_connections': self.max_connections,
            **self.connection_kwargs
        }

    def get_cluster_config(self) -> Dict[str, Any]:
        """获取集群配置"""
        if not self.cluster_enabled:
            return {}

        return {
            'startup_nodes': [
                {'host': node.split(':')[0], 'port': int(node.split(':')[1])}
                for node in self.cluster_nodes
            ],
            'decode_responses': self.decode_responses,
            'skip_full_coverage_check': True,
            'max_connections_per_node': self.max_connections // len(self.cluster_nodes) if self.cluster_nodes else 10
        }

    def get_sentinel_config(self) -> Dict[str, Any]:
        """获取哨兵配置"""
        if not self.sentinel_enabled:
            return {}

        return {
            'sentinels': [
                (host.split(':')[0], int(host.split(':')[1]))
                for host in self.sentinel_hosts
            ],
            'service_name': self.sentinel_service_name,
            'socket_timeout': self.socket_timeout,
            'socket_connect_timeout': self.socket_connect_timeout,
            'decode_responses': self.decode_responses,
            'password': self.password,
            'db': self.database
        }

    class Config:
        env_prefix = 'REDIS_'
        env_file = '.env'
        case_sensitive = False
from pydantic import computed_field
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any


class RedisConfig(BaseSettings):
    """Redis配置"""

    # 基础连接配置
    host: str = '127.0.0.1'
    port: int = 6379
    password: Optional[str] = None
    database: int = 0

    # 连接池配置
    max_connections: int = 100
    retry_on_timeout: bool = True
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = {}

    # 集群配置
    cluster_enabled: bool = False
    cluster_nodes: list = []

    # 哨兵配置
    sentinel_enabled: bool = False
    sentinel_hosts: list = []
    sentinel_service_name: str = 'mymaster'

    # SSL配置
    ssl_enabled: bool = False
    ssl_cert_reqs: str = 'required'
    ssl_ca_certs: Optional[str] = None
    ssl_certfile: Optional[str] = None
    ssl_keyfile: Optional[str] = None

    # 性能配置
    decode_responses: bool = True
    encoding: str = 'utf-8'
    health_check_interval: int = 30

    # 缓存配置
    default_ttl: int = 3600  # 默认1小时
    key_prefix: str = 'mxtt:'

    @computed_field
    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        if self.password:
            auth = f":{self.password}@"
        else:
            auth = ""
        
        protocol = "rediss" if self.ssl_enabled else "redis"
        return f"{protocol}://{auth}{self.host}:{self.port}/{self.database}"

    @computed_field
    @property
    def connection_kwargs(self) -> Dict[str, Any]:
        """Redis连接参数"""
        kwargs = {
            'host': self.host,
            'port': self.port,
            'db': self.database,
            'password': self.password,
            'socket_timeout': self.socket_timeout,
            'socket_connect_timeout': self.socket_connect_timeout,
            'socket_keepalive': self.socket_keepalive,
            'socket_keepalive_options': self.socket_keepalive_options,
            'retry_on_timeout': self.retry_on_timeout,
            'decode_responses': self.decode_responses,
            'encoding': self.encoding,
            'health_check_interval': self.health_check_interval,
        }

        # SSL配置
        if self.ssl_enabled:
            kwargs.update({
                'ssl': True,
                'ssl_cert_reqs': self.ssl_cert_reqs,
                'ssl_ca_certs': self.ssl_ca_certs,
                'ssl_certfile': self.ssl_certfile,
                'ssl_keyfile': self.ssl_keyfile,
            })

        return kwargs

    @computed_field
    @property
    def connection_pool_kwargs(self) -> Dict[str, Any]:
        """Redis连接池参数"""
        return {
            'max_connections': self.max_connections,
            **self.connection_kwargs
        }

    def get_cluster_config(self) -> Dict[str, Any]:
        """获取集群配置"""
        if not self.cluster_enabled:
            return {}

        return {
            'startup_nodes': [
                {'host': node.split(':')[0], 'port': int(node.split(':')[1])}
                for node in self.cluster_nodes
            ],
            'decode_responses': self.decode_responses,
            'skip_full_coverage_check': True,
            'max_connections_per_node': self.max_connections // len(self.cluster_nodes) if self.cluster_nodes else 10
        }

    def get_sentinel_config(self) -> Dict[str, Any]:
        """获取哨兵配置"""
        if not self.sentinel_enabled:
            return {}

        return {
            'sentinels': [
                (host.split(':')[0], int(host.split(':')[1]))
                for host in self.sentinel_hosts
            ],
            'service_name': self.sentinel_service_name,
            'socket_timeout': self.socket_timeout,
            'socket_connect_timeout': self.socket_connect_timeout,
            'decode_responses': self.decode_responses,
            'password': self.password,
            'db': self.database
        }

    class Config:
        env_prefix = 'REDIS_'
        env_file = '.env'
        case_sensitive = False
