"""
性能监控中间件模块
"""
import time
import uuid
from typing import Callable
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..logging.structured_logger import structured_logger, LogContext, request_id_var, user_id_var
from ..monitoring.metrics import metrics_collector
from ..exceptions import RateLimitException


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    def __init__(self, app: FastAPI):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get('user-agent', '')
        
        # 设置请求上下文
        with LogContext(request_id=request_id):
            start_time = time.time()
            
            # 记录请求开始
            structured_logger.info(
                "请求开始",
                method=request.method,
                path=request.url.path,
                query_params=str(request.query_params),
                client_ip=client_ip,
                user_agent=user_agent
            )

            try:
                # 处理请求
                response = await call_next(request)
                
                # 计算响应时间
                process_time = time.time() - start_time
                
                # 判断是否为错误响应
                is_error = response.status_code >= 400
                is_slow = process_time > 1.0  # 超过1秒认为是慢请求
                
                # 记录指标
                metrics_collector.record_request(
                    response_time=process_time,
                    is_error=is_error,
                    endpoint=request.url.path
                )
                
                # 添加响应头
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Process-Time"] = str(process_time)
                
                # 记录访问日志
                structured_logger.access(
                    request_id=request_id,
                    method=request.method,
                    path=request.url.path,
                    status_code=response.status_code,
                    response_time=process_time,
                    user_agent=user_agent,
                    client_ip=client_ip,
                    user_id=user_id_var.get('')
                )
                
                # 记录慢请求
                if is_slow:
                    structured_logger.warning(
                        "慢请求检测",
                        method=request.method,
                        path=request.url.path,
                        response_time=process_time,
                        status_code=response.status_code,
                        client_ip=client_ip
                    )
                
                # 记录错误请求
                if is_error:
                    structured_logger.warning(
                        "错误请求",
                        method=request.method,
                        path=request.url.path,
                        status_code=response.status_code,
                        response_time=process_time,
                        client_ip=client_ip
                    )
                
                return response
                
            except Exception as e:
                # 计算响应时间
                process_time = time.time() - start_time
                
                # 记录异常指标
                metrics_collector.record_request(
                    response_time=process_time,
                    is_error=True,
                    endpoint=request.url.path
                )
                
                # 记录异常日志
                structured_logger.error(
                    "请求处理异常",
                    method=request.method,
                    path=request.url.path,
                    response_time=process_time,
                    error=str(e),
                    client_ip=client_ip,
                    exc_info=True
                )
                
                raise

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        # 从连接信息获取
        if request.client:
            return request.client.host
        
        return 'unknown'


class RequestContextMiddleware(BaseHTTPMiddleware):
    """请求上下文中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 设置请求状态
        request.state.request_id = request_id
        request.state.start_time = time.time()
        
        # 设置上下文变量
        token = request_id_var.set(request_id)
        
        try:
            response = await call_next(request)
            return response
        finally:
            # 清理上下文
            request_id_var.reset(token)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'",
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        response = await call_next(request)
        
        # 添加安全头
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""

    def __init__(self, app: FastAPI, rate_limiter=None):
        super().__init__(app)
        self.rate_limiter = rate_limiter
        self.enabled = rate_limiter is not None

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        if not self.enabled:
            return await call_next(request)

        # 生成限流键
        client_ip = self._get_client_ip(request)
        endpoint = request.url.path
        rate_key = f"global_rate_limit:{client_ip}:{endpoint}"

        try:
            # 检查限流
            result = await self.rate_limiter.is_allowed(rate_key, 100, 60)
            
            if not result['allowed']:
                structured_logger.warning(
                    "请求被全局限流",
                    client_ip=client_ip,
                    endpoint=endpoint,
                    limit=result['limit'],
                    remaining=result['remaining']
                )
                
                raise RateLimitException(
                    limit=result['limit'],
                    window=60,
                    detail=f"请求频率超限，请{result['retry_after']}秒后重试"
                )

            # 处理请求
            response = await call_next(request)
            
            # 添加限流头信息
            response.headers['X-RateLimit-Limit'] = str(result['limit'])
            response.headers['X-RateLimit-Remaining'] = str(result['remaining'])
            response.headers['X-RateLimit-Reset'] = str(result['reset_time'])
            
            return response

        except RateLimitException:
            raise
        except Exception as e:
            structured_logger.error(f"限流中间件异常: {e}")
            # 限流器异常时允许请求通过
            return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        if request.client:
            return request.client.host
        
        return 'unknown'


class CompressionMiddleware(BaseHTTPMiddleware):
    """压缩中间件"""

    def __init__(self, app: FastAPI, minimum_size: int = 1000):
        super().__init__(app)
        self.minimum_size = minimum_size

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        response = await call_next(request)
        
        # 检查是否需要压缩
        if self._should_compress(request, response):
            # 这里可以添加压缩逻辑
            # 实际实现中可以使用 gzip 或其他压缩算法
            pass
        
        return response

    def _should_compress(self, request: Request, response: Response) -> bool:
        """判断是否应该压缩"""
        # 检查Accept-Encoding头
        accept_encoding = request.headers.get('accept-encoding', '')
        if 'gzip' not in accept_encoding:
            return False
        
        # 检查Content-Type
        content_type = response.headers.get('content-type', '')
        compressible_types = [
            'text/', 'application/json', 'application/javascript',
            'application/xml', 'application/rss+xml'
        ]
        
        if not any(content_type.startswith(ct) for ct in compressible_types):
            return False
        
        # 检查内容长度
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) < self.minimum_size:
            return False
        
        return True
