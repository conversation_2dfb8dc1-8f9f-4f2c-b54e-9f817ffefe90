"""
统一配置管理模块
"""
import os
import sys
import argparse
from functools import lru_cache
from dotenv import load_dotenv
from typing import Optional

from .app import AppConfig
from .database import DatabaseConfig
from .redis import RedisConfig


class Settings:
    """统一配置管理器"""

    def __init__(self):
        self._parse_cli_args()
        self._load_env_file()
        self._app: Optional[AppConfig] = None
        self._database: Optional[DatabaseConfig] = None
        self._redis: Optional[RedisConfig] = None

    @property
    @lru_cache()
    def app(self) -> AppConfig:
        """获取应用配置"""
        if self._app is None:
            self._app = AppConfig()
        return self._app

    @property
    @lru_cache()
    def database(self) -> DatabaseConfig:
        """获取数据库配置"""
        if self._database is None:
            self._database = DatabaseConfig()
        return self._database

    @property
    @lru_cache()
    def redis(self) -> RedisConfig:
        """获取Redis配置"""
        if self._redis is None:
            self._redis = RedisConfig()
        return self._redis

    def _parse_cli_args(self):
        """解析命令行参数"""
        if 'uvicorn' in sys.argv[0]:
            # 使用uvicorn启动时，命令行参数需要按照uvicorn的文档进行配置，无法自定义参数
            pass
        else:
            # 使用argparse定义命令行参数
            parser = argparse.ArgumentParser(description='MXTT-FastAPI 应用启动参数')
            parser.add_argument('--env', type=str, default='', help='运行环境 (dev/test/staging/prod)')
            parser.add_argument('--config', type=str, default='', help='配置文件路径')
            parser.add_argument('--debug', action='store_true', help='启用调试模式')
            parser.add_argument('--host', type=str, default='', help='服务器主机地址')
            parser.add_argument('--port', type=int, default=0, help='服务器端口')
            
            # 解析命令行参数
            args = parser.parse_args()
            
            # 设置环境变量
            if args.env:
                os.environ['APP_ENVIRONMENT'] = args.env
            if args.debug:
                os.environ['APP_DEBUG'] = 'true'
            if args.host:
                os.environ['APP_HOST'] = args.host
            if args.port:
                os.environ['APP_PORT'] = str(args.port)

    def _load_env_file(self):
        """加载环境配置文件"""
        # 读取运行环境
        env = os.environ.get('APP_ENVIRONMENT', 'development')
        
        # 根据环境加载对应的配置文件
        env_files = [
            f'.env.{env}',  # 环境特定配置
            f'.env.{env}.local',  # 环境特定本地配置
            '.env.local',  # 本地配置
            '.env',  # 默认配置
        ]
        
        for env_file in env_files:
            if os.path.exists(env_file):
                load_dotenv(env_file, override=False)
                print(f"已加载配置文件: {env_file}")

    def reload_config(self):
        """重新加载配置"""
        # 清除缓存
        self.app.cache_clear()
        self.database.cache_clear()
        self.redis.cache_clear()
        
        # 重置实例
        self._app = None
        self._database = None
        self._redis = None
        
        # 重新加载环境文件
        self._load_env_file()

    def get_config_summary(self) -> dict:
        """获取配置摘要"""
        return {
            'app': {
                'name': self.app.name,
                'version': self.app.version,
                'environment': self.app.environment,
                'debug': self.app.debug,
                'host': self.app.host,
                'port': self.app.port,
            },
            'database': {
                'type': self.database.type,
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database,
                'pool_size': self.database.pool_size,
            },
            'redis': {
                'host': self.redis.host,
                'port': self.redis.port,
                'database': self.redis.database,
                'max_connections': self.redis.max_connections,
            }
        }

    def validate_config(self) -> list:
        """验证配置"""
        errors = []
        
        # 验证应用配置
        if not self.app.secret_key or self.app.secret_key == "your-secret-key-here":
            errors.append("应用密钥未设置或使用默认值，存在安全风险")
        
        if self.app.is_production and self.app.debug:
            errors.append("生产环境不应启用调试模式")
        
        # 验证数据库配置
        if not self.database.password and self.database.type != 'sqlite':
            errors.append("数据库密码未设置")
        
        # 验证Redis配置
        if self.redis.password is None and not self.app.is_development:
            errors.append("非开发环境建议设置Redis密码")
        
        return errors


# 全局配置实例
settings = Settings()

# 向后兼容的配置别名
AppConfig = settings.app
DatabaseConfig = settings.database
RedisConfig = settings.redis
