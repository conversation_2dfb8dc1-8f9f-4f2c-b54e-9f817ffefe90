"""
应用核心配置模块
"""
from pydantic_settings import BaseSettings


class AppConfig(BaseSettings):
    """应用核心配置"""
    
    name: str = 'MXTT-FastAPI'
    version: str = '1.0.0'
    environment: str = 'development'
    host: str = '0.0.0.0'
    port: int = 9099
    reload: bool = True
    root_path: str = '/dev-api'
    ip_location_query: bool = True
    same_time_login: bool = True
    
    class Config:
        env_prefix = 'APP_'
        env_file = '.env'
