# 开发环境配置文件

# 应用配置
APP_NAME=MXTT-FastAPI
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_HOST=0.0.0.0
APP_PORT=9099
APP_RELOAD=true
APP_ROOT_PATH=/dev-api

# 数据库配置
DB_TYPE=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=1234
DB_DATABASE=ruoyi-fastapi
DB_ECHO=false

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_DATABASE=2

# JWT配置
JWT_SECRET_KEY=b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
JWT_REDIS_EXPIRE_MINUTES=30

# 上传配置
UPLOAD_PREFIX=/profile
UPLOAD_PATH=storage/uploads
UPLOAD_MACHINE=A
UPLOAD_DOWNLOAD_PATH=storage/downloads

# 代码生成配置
GEN_AUTHOR=mengqb
GEN_PACKAGE_NAME=admin.system
GEN_AUTO_REMOVE_PRE=false
GEN_TABLE_PREFIX=sys_
GEN_ALLOW_OVERWRITE=false
GEN_PATH=storage/generated
