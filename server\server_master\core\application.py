"""
应用工厂模块
"""
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI

from config import settings
from .database.database_manager import DatabaseManager
from .cache.cache_manager import CacheManager
from .security.jwt_manager import JWTManager
from .exceptions.handler import setup_exception_handlers
from .middleware.handler import setup_middleware
from .monitoring.health_check import health_checker
from .monitoring.metrics import performance_monitor
from .logging.structured_logger import structured_logger


class ApplicationFactory:
    """应用工厂类，负责创建和配置FastAPI应用"""
    
    @classmethod
    def create_app(cls) -> FastAPI:
        """创建FastAPI应用实例"""
        # 获取FastAPI配置
        fastapi_config = settings.app.get_fastapi_config()
        
        app = FastAPI(
            **fastapi_config,
            lifespan=cls._lifespan,
        )
        
        # 设置应用组件
        cls._setup_exception_handlers(app)
        cls._setup_middleware(app)
        cls._setup_routers(app)
        cls._setup_static_files(app)
        
        return app
    
    @classmethod
    @asynccontextmanager
    async def _lifespan(cls, app: FastAPI):
        """应用生命周期管理"""
        structured_logger.info(f'{settings.app.name} v{settings.app.version} 开始启动')
        
        try:
            # 验证配置
            await cls._validate_configuration()
            
            # 初始化数据库
            await cls._init_database()
            
            # 初始化缓存
            await cls._init_cache(app)
            
            # 初始化安全组件
            await cls._init_security(app)
            
            # 初始化监控
            await cls._init_monitoring()
            
            # 初始化调度器
            await cls._init_scheduler()
            
            structured_logger.info(f'{settings.app.name} 启动成功')
            
            yield
            
        except Exception as e:
            structured_logger.error(f'{settings.app.name} 启动失败: {e}')
            raise
        finally:
            # 清理资源
            await cls._cleanup_resources(app)
            structured_logger.info(f'{settings.app.name} 关闭完成')
    
    @classmethod
    async def _validate_configuration(cls):
        """验证配置"""
        structured_logger.info("开始验证配置")
        
        # 验证配置
        config_errors = settings.validate_config()
        if config_errors:
            for error in config_errors:
                structured_logger.warning(f"配置警告: {error}")
        
        # 打印配置摘要
        config_summary = settings.get_config_summary()
        structured_logger.info("配置摘要", **config_summary)
        
        structured_logger.info("配置验证完成")
    
    @classmethod
    async def _init_database(cls):
        """初始化数据库"""
        structured_logger.info("开始初始化数据库")
        
        try:
            # 初始化数据库管理器
            DatabaseManager.init_database(
                settings.database.async_database_url,
                **settings.database.engine_options
            )
            
            # 健康检查
            if await DatabaseManager.health_check():
                structured_logger.info("数据库连接正常")
            else:
                raise Exception("数据库健康检查失败")
                
        except Exception as e:
            structured_logger.error(f"数据库初始化失败: {e}")
            raise
    
    @classmethod
    async def _init_cache(cls, app: FastAPI):
        """初始化缓存"""
        structured_logger.info("开始初始化缓存")
        
        try:
            # 创建缓存管理器
            cache_manager = await CacheManager.create_instance(
                settings.redis.redis_url,
                **settings.redis.connection_pool_kwargs
            )
            
            # 设置应用状态
            app.state.cache = cache_manager
            
            # 健康检查
            if await cache_manager.health_check():
                structured_logger.info("缓存连接正常")
            else:
                raise Exception("缓存健康检查失败")
                
        except Exception as e:
            structured_logger.error(f"缓存初始化失败: {e}")
            raise
    
    @classmethod
    async def _init_security(cls, app: FastAPI):
        """初始化安全组件"""
        structured_logger.info("开始初始化安全组件")
        
        try:
            # 创建JWT管理器
            jwt_manager = JWTManager(
                secret_key=settings.app.secret_key,
                algorithm=settings.app.jwt_algorithm
            )
            
            # 设置缓存管理器
            if hasattr(app.state, 'cache'):
                jwt_manager.set_cache_manager(app.state.cache)
            
            # 设置应用状态
            app.state.jwt_manager = jwt_manager
            
            structured_logger.info("安全组件初始化完成")
            
        except Exception as e:
            structured_logger.error(f"安全组件初始化失败: {e}")
            raise
    
    @classmethod
    async def _init_monitoring(cls):
        """初始化监控"""
        structured_logger.info("开始初始化监控系统")
        
        try:
            if settings.app.enable_performance_monitoring:
                # 启用性能监控
                performance_monitor.enable_monitoring()
                structured_logger.info("性能监控已启用")
            
            # 可以添加其他监控组件的初始化
            
            structured_logger.info("监控系统初始化完成")
            
        except Exception as e:
            structured_logger.error(f"监控系统初始化失败: {e}")
            # 监控系统失败不应该阻止应用启动
    
    @classmethod
    async def _init_scheduler(cls):
        """初始化调度器"""
        if not settings.app.scheduler_enabled:
            return
            
        structured_logger.info("开始初始化调度器")
        
        try:
            # 这里可以初始化APScheduler或其他调度器
            # from .scheduler.scheduler_service import SchedulerService
            # await SchedulerService.init_system_scheduler()
            
            structured_logger.info("调度器初始化完成")
            
        except Exception as e:
            structured_logger.error(f"调度器初始化失败: {e}")
            # 调度器失败不应该阻止应用启动
    
    @classmethod
    async def _cleanup_resources(cls, app: FastAPI):
        """清理资源"""
        structured_logger.info("开始清理资源")
        
        try:
            # 关闭缓存连接
            if hasattr(app.state, 'cache'):
                await CacheManager.close()
            
            # 关闭数据库连接
            await DatabaseManager.close()
            
            # 关闭调度器
            if settings.app.scheduler_enabled:
                # await SchedulerService.close_system_scheduler()
                pass
            
            structured_logger.info("资源清理完成")
            
        except Exception as e:
            structured_logger.error(f"资源清理失败: {e}")
    
    @classmethod
    def _setup_exception_handlers(cls, app: FastAPI):
        """设置异常处理器"""
        setup_exception_handlers(app)
    
    @classmethod
    def _setup_middleware(cls, app: FastAPI):
        """设置中间件"""
        setup_middleware(app, settings.app)
    
    @classmethod
    def _setup_routers(cls, app: FastAPI):
        """设置路由"""
        from .routing import setup_routers
        setup_routers(app)

        # 添加健康检查路由
        cls._setup_health_routes(app)
    
    @classmethod
    def _setup_health_routes(cls, app: FastAPI):
        """设置健康检查路由"""
        @app.get("/health", tags=["健康检查"])
        async def health_check():
            """健康检查端点"""
            return await health_checker.check_all()
        
        @app.get("/health/{check_name}", tags=["健康检查"])
        async def single_health_check(check_name: str):
            """单个健康检查端点"""
            return await health_checker.check_single(check_name)
        
        @app.get("/metrics", tags=["监控"])
        async def metrics():
            """指标端点"""
            from .monitoring.metrics import metrics_collector
            system_metrics = metrics_collector.collect_system_metrics()
            app_metrics = metrics_collector.collect_application_metrics()
            
            return {
                "system": system_metrics.to_dict(),
                "application": app_metrics.to_dict(),
                "endpoints": metrics_collector.get_all_endpoint_metrics()
            }
    
    @classmethod
    def _setup_static_files(cls, app: FastAPI):
        """设置静态文件"""
        # 这里可以设置静态文件服务
        # from fastapi.staticfiles import StaticFiles
        # app.mount("/static", StaticFiles(directory="static"), name="static")
        pass


# 便捷函数
def create_app() -> FastAPI:
    """创建应用实例的便捷函数"""
    return ApplicationFactory.create_app()
