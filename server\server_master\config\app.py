"""
应用配置模块
"""
from pydantic_settings import BaseSettings
from typing import List, Optional, Dict, Any
from enum import Enum


class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class AppConfig(BaseSettings):
    """应用配置"""

    # 基础配置
    name: str = "MXTT-FastAPI"
    version: str = "2.0.0"
    description: str = "MXTT-FastAPI 企业级后台管理系统"
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 9099
    root_path: str = ""
    reload: bool = True
    workers: int = 1

    # 安全配置
    secret_key: str = "your-secret-key-here"
    allowed_hosts: List[str] = ["*"]
    cors_origins: List[str] = ["*"]
    cors_methods: List[str] = ["*"]
    cors_headers: List[str] = ["*"]
    cors_credentials: bool = True

    # JWT配置
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7

    # 日志配置
    log_level: LogLevel = LogLevel.INFO
    log_format: str = "json"
    log_rotation: str = "1 day"
    log_retention: str = "30 days"
    log_compression: str = "zip"

    # API配置
    api_prefix: str = "/api"
    docs_url: Optional[str] = "/docs"
    redoc_url: Optional[str] = "/redoc"
    openapi_url: Optional[str] = "/openapi.json"

    # 中间件配置
    enable_gzip: bool = True
    gzip_minimum_size: int = 1000
    enable_cors: bool = True
    enable_trace: bool = True

    # 限流配置
    rate_limit_enabled: bool = True
    default_rate_limit: int = 100
    default_rate_window: int = 60

    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30
    enable_performance_monitoring: bool = True

    # 文件上传配置
    upload_max_size: int = 10 * 1024 * 1024  # 10MB
    upload_allowed_extensions: List[str] = [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx',
        '.txt', '.zip', '.rar'
    ]
    upload_path: str = "storage/uploads"

    # 缓存配置
    cache_enabled: bool = True
    cache_default_ttl: int = 3600

    # 数据库配置
    db_echo: bool = False
    db_pool_size: int = 20
    db_max_overflow: int = 30

    # 任务调度配置
    scheduler_enabled: bool = True
    scheduler_timezone: str = "Asia/Shanghai"

    # 代码生成配置
    generator_enabled: bool = True
    generator_template_path: str = "templates"
    generator_output_path: str = "storage/generated"

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == Environment.DEVELOPMENT

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == Environment.PRODUCTION

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == Environment.TESTING

    def get_cors_config(self) -> Dict[str, Any]:
        """获取CORS配置"""
        return {
            'allow_origins': self.cors_origins,
            'allow_methods': self.cors_methods,
            'allow_headers': self.cors_headers,
            'allow_credentials': self.cors_credentials,
        }

    def get_uvicorn_config(self) -> Dict[str, Any]:
        """获取Uvicorn配置"""
        config = {
            'host': self.host,
            'port': self.port,
            'reload': self.reload and self.is_development,
            'workers': 1 if self.is_development else self.workers,
            'log_level': self.log_level.lower(),
            'access_log': self.is_development,
        }

        if self.root_path:
            config['root_path'] = self.root_path

        return config

    def get_fastapi_config(self) -> Dict[str, Any]:
        """获取FastAPI配置"""
        config = {
            'title': self.name,
            'description': self.description,
            'version': self.version,
            'debug': self.debug and self.is_development,
        }

        # 生产环境隐藏文档
        if self.is_production:
            config.update({
                'docs_url': None,
                'redoc_url': None,
                'openapi_url': None,
            })
        else:
            config.update({
                'docs_url': self.docs_url,
                'redoc_url': self.redoc_url,
                'openapi_url': self.openapi_url,
            })

        return config

    class Config:
        env_prefix = 'APP_'
        env_file = '.env'
        case_sensitive = False
