version: '3.8'

services:
  # FastAPI应用（开发模式）
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: mxtt-fastapi-dev
    restart: unless-stopped
    ports:
      - "9099:9099"
    environment:
      - APP_ENVIRONMENT=development
      - APP_DEBUG=true
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=1234
      - DB_DATABASE=ruoyi-fastapi
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mxtt-dev-network
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9099", "--reload"]

  # MySQL数据库（开发环境）
  mysql:
    image: mysql:8.0
    container_name: mxtt-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 1234
      MYSQL_DATABASE: ruoyi-fastapi
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./scripts/mysql/init-dev.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mxtt-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p1234"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存（开发环境）
  redis:
    image: redis:7-alpine
    container_name: mxtt-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - mxtt-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # phpMyAdmin（开发环境数据库管理）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: mxtt-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: 1234
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - mxtt-dev-network

  # Redis Commander（开发环境Redis管理）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mxtt-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - mxtt-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  mxtt-dev-network:
    driver: bridge
