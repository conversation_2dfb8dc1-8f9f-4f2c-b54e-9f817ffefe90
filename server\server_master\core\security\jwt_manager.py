"""
JWT管理器模块
"""
import jwt
import secrets
import base64
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from ..logging.structured_logger import structured_logger
from ..exceptions import AuthenticationException, ErrorCode
from ..cache.cache_manager import CacheManager


class JWTManager:
    """JWT管理器 - 增强安全性"""

    def __init__(self, secret_key: str, algorithm: str = 'HS256'):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire = timedelta(minutes=30)  # 缩短访问令牌时间
        self.refresh_token_expire = timedelta(days=7)  # 刷新令牌7天
        self.cache_manager = None

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    async def create_access_token(self, data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        try:
            to_encode = data.copy()
            expire = datetime.now(timezone.utc) + self.access_token_expire
            to_encode.update({
                'exp': expire,
                'iat': datetime.now(timezone.utc),
                'type': 'access',
                'jti': secrets.token_urlsafe(16)  # JWT ID
            })

            token = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
            
            # 缓存令牌信息
            if self.cache_manager:
                await self.cache_manager.set(
                    f"access_token:{to_encode['jti']}", 
                    data, 
                    int(self.access_token_expire.total_seconds())
                )

            structured_logger.debug(
                "访问令牌创建成功",
                user_id=data.get('user_id'),
                jti=to_encode['jti']
            )

            return token
        except Exception as e:
            structured_logger.error(f"创建访问令牌失败: {e}")
            raise AuthenticationException(ErrorCode.TOKEN_INVALID, "令牌创建失败")

    async def create_refresh_token(self, user_id: int) -> str:
        """创建刷新令牌"""
        try:
            to_encode = {
                'user_id': user_id,
                'exp': datetime.now(timezone.utc) + self.refresh_token_expire,
                'iat': datetime.now(timezone.utc),
                'type': 'refresh',
                'jti': secrets.token_urlsafe(16)
            }

            token = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

            # 缓存刷新令牌
            if self.cache_manager:
                await self.cache_manager.set(
                    f"refresh_token:{to_encode['jti']}", 
                    {'user_id': user_id}, 
                    int(self.refresh_token_expire.total_seconds())
                )

            structured_logger.debug(
                "刷新令牌创建成功",
                user_id=user_id,
                jti=to_encode['jti']
            )

            return token
        except Exception as e:
            structured_logger.error(f"创建刷新令牌失败: {e}")
            raise AuthenticationException(ErrorCode.TOKEN_INVALID, "令牌创建失败")

    async def verify_token(self, token: str, token_type: str = 'access') -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            if payload.get('type') != token_type:
                structured_logger.warning(f"令牌类型不匹配: 期望{token_type}, 实际{payload.get('type')}")
                return None

            # 检查令牌是否在黑名单中
            if await self._is_token_blacklisted(payload.get('jti')):
                structured_logger.warning(f"令牌已被列入黑名单: {payload.get('jti')}")
                return None

            # 验证缓存中的令牌
            if self.cache_manager:
                cache_key = f"{token_type}_token:{payload.get('jti')}"
                cached_data = await self.cache_manager.get(cache_key)
                if not cached_data:
                    structured_logger.warning(f"令牌不在缓存中: {payload.get('jti')}")
                    return None

            structured_logger.debug(
                "令牌验证成功",
                token_type=token_type,
                jti=payload.get('jti'),
                user_id=payload.get('user_id')
            )

            return payload
        except jwt.ExpiredSignatureError:
            structured_logger.warning("令牌已过期")
            raise AuthenticationException(ErrorCode.TOKEN_EXPIRED, "令牌已过期")
        except jwt.InvalidTokenError as e:
            structured_logger.warning(f"令牌无效: {e}")
            raise AuthenticationException(ErrorCode.TOKEN_INVALID, "令牌无效")
        except Exception as e:
            structured_logger.error(f"令牌验证失败: {e}")
            raise AuthenticationException(ErrorCode.TOKEN_INVALID, "令牌验证失败")

    async def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """刷新访问令牌"""
        try:
            # 验证刷新令牌
            payload = await self.verify_token(refresh_token, 'refresh')
            if not payload:
                raise AuthenticationException(ErrorCode.TOKEN_INVALID, "刷新令牌无效")

            user_id = payload.get('user_id')
            if not user_id:
                raise AuthenticationException(ErrorCode.TOKEN_INVALID, "刷新令牌缺少用户信息")

            # 创建新的访问令牌
            new_access_token = await self.create_access_token({'user_id': user_id})

            structured_logger.info(
                "访问令牌刷新成功",
                user_id=user_id,
                old_jti=payload.get('jti')
            )

            return {
                'access_token': new_access_token,
                'token_type': 'bearer'
            }
        except Exception as e:
            structured_logger.error(f"刷新访问令牌失败: {e}")
            raise

    async def blacklist_token(self, jti: str, expire_time: datetime):
        """将令牌加入黑名单"""
        if not self.cache_manager:
            return

        try:
            # 计算剩余过期时间
            remaining_time = expire_time - datetime.now(timezone.utc)
            if remaining_time.total_seconds() > 0:
                await self.cache_manager.set(
                    f"blacklist:{jti}", 
                    True, 
                    int(remaining_time.total_seconds())
                )

            structured_logger.info(f"令牌已加入黑名单: {jti}")
        except Exception as e:
            structured_logger.error(f"加入黑名单失败: {e}")

    async def _is_token_blacklisted(self, jti: str) -> bool:
        """检查令牌是否在黑名单中"""
        if not self.cache_manager or not jti:
            return False

        try:
            return await self.cache_manager.exists(f"blacklist:{jti}")
        except Exception as e:
            structured_logger.error(f"检查黑名单失败: {e}")
            return False

    async def revoke_token(self, token: str):
        """撤销令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            jti = payload.get('jti')
            exp = payload.get('exp')
            
            if jti and exp:
                expire_time = datetime.fromtimestamp(exp, tz=timezone.utc)
                await self.blacklist_token(jti, expire_time)

            structured_logger.info(f"令牌已撤销: {jti}")
        except Exception as e:
            structured_logger.error(f"撤销令牌失败: {e}")

    async def revoke_all_user_tokens(self, user_id: int):
        """撤销用户的所有令牌"""
        if not self.cache_manager:
            return

        try:
            # 删除用户相关的所有令牌缓存
            await self.cache_manager.invalidate_pattern(f"*token:*")
            
            structured_logger.info(f"用户所有令牌已撤销: {user_id}")
        except Exception as e:
            structured_logger.error(f"撤销用户令牌失败: {e}")


class PasswordSecurity:
    """密码安全管理"""

    @staticmethod
    def generate_salt() -> str:
        """生成盐值"""
        return secrets.token_urlsafe(32)

    @staticmethod
    def hash_password(password: str, salt: str) -> str:
        """使用 PBKDF2 哈希密码"""
        try:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt.encode(),
                iterations=100000,  # 增加迭代次数
            )
            key = kdf.derive(password.encode())
            return base64.b64encode(key).decode()
        except Exception as e:
            structured_logger.error(f"密码哈希失败: {e}")
            raise

    @staticmethod
    def verify_password(password: str, salt: str, hashed: str) -> bool:
        """验证密码"""
        try:
            return PasswordSecurity.hash_password(password, salt) == hashed
        except Exception as e:
            structured_logger.error(f"密码验证失败: {e}")
            return False

    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """检查密码强度"""
        import re

        checks = {
            'length': len(password) >= 8,
            'uppercase': bool(re.search(r'[A-Z]', password)),
            'lowercase': bool(re.search(r'[a-z]', password)),
            'digit': bool(re.search(r'\d', password)),
            'special': bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password)),
        }

        score = sum(checks.values())
        strength = 'weak' if score < 3 else 'medium' if score < 5 else 'strong'

        return {
            'score': score,
            'strength': strength,
            'checks': checks,
            'valid': score >= 3
        }

    @staticmethod
    def generate_secure_password(length: int = 12) -> str:
        """生成安全密码"""
        import string
        
        # 确保包含各种字符类型
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 验证生成的密码强度
        strength = PasswordSecurity.check_password_strength(password)
        if not strength['valid']:
            # 如果强度不够，递归重新生成
            return PasswordSecurity.generate_secure_password(length)
        
        return password


class TokenBlacklist:
    """令牌黑名单管理"""

    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager

    async def add_token(self, jti: str, expire_time: datetime):
        """添加令牌到黑名单"""
        remaining_time = expire_time - datetime.now(timezone.utc)
        if remaining_time.total_seconds() > 0:
            await self.cache_manager.set(
                f"blacklist:{jti}", 
                True, 
                int(remaining_time.total_seconds())
            )

    async def is_blacklisted(self, jti: str) -> bool:
        """检查令牌是否在黑名单中"""
        return await self.cache_manager.exists(f"blacklist:{jti}")

    async def remove_token(self, jti: str):
        """从黑名单中移除令牌"""
        await self.cache_manager.delete(f"blacklist:{jti}")

    async def clear_expired(self):
        """清理过期的黑名单条目（Redis会自动处理，这里只是占位）"""
        pass
