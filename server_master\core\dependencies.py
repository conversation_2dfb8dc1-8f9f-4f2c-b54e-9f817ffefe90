"""
依赖注入模块
"""
from functools import lru_cache
from typing import AsyncGenerator, Type, TypeVar, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends

from .database.database_manager import DatabaseManager
from .cache.cache_manager import CacheManager
from .logging.structured_logger import structured_logger

T = TypeVar('T')


@lru_cache()
def get_service_factory():
    """服务工厂单例"""
    return ServiceFactory()


class ServiceFactory:
    """服务工厂类"""

    def __init__(self):
        self._services: Dict[Type, Type] = {}
        self._repositories: Dict[Type, Type] = {}
        self._singletons: Dict[Type, Any] = {}

    def register_service(self, service_type: Type[T], service_class: Type[T]):
        """注册服务"""
        self._services[service_type] = service_class
        structured_logger.info(f"注册服务: {service_type.__name__} -> {service_class.__name__}")

    def register_repository(self, repository_type: Type[T], repository_class: Type[T]):
        """注册仓储"""
        self._repositories[repository_type] = repository_class
        structured_logger.info(f"注册仓储: {repository_type.__name__} -> {repository_class.__name__}")

    def register_singleton(self, service_type: Type[T], instance: T):
        """注册单例服务"""
        self._singletons[service_type] = instance
        structured_logger.info(f"注册单例: {service_type.__name__}")

    def get_service(self, service_type: Type[T], session: AsyncSession, **kwargs) -> T:
        """获取服务实例"""
        # 检查是否为单例
        if service_type in self._singletons:
            return self._singletons[service_type]

        service_class = self._services.get(service_type)
        if not service_class:
            raise ValueError(f"Service {service_type.__name__} not registered")

        # 创建服务实例
        try:
            if hasattr(service_class, '__init__'):
                import inspect
                sig = inspect.signature(service_class.__init__)
                params = {}
                
                for param_name, param in sig.parameters.items():
                    if param_name == 'self':
                        continue
                    elif param_name == 'session':
                        params['session'] = session
                    elif param_name in kwargs:
                        params[param_name] = kwargs[param_name]
                    elif param.annotation in self._repositories:
                        # 自动注入仓储
                        repo_class = self._repositories[param.annotation]
                        params[param_name] = repo_class(session)

                return service_class(**params)
            else:
                return service_class(session)
        except Exception as e:
            structured_logger.error(f"创建服务实例失败: {service_type.__name__}, 错误: {e}")
            raise

    def get_repository(self, repository_type: Type[T], session: AsyncSession, model_class=None) -> T:
        """获取仓储实例"""
        repository_class = self._repositories.get(repository_type)
        if not repository_class:
            raise ValueError(f"Repository {repository_type.__name__} not registered")

        try:
            if model_class:
                return repository_class(session, model_class)
            else:
                return repository_class(session)
        except Exception as e:
            structured_logger.error(f"创建仓储实例失败: {repository_type.__name__}, 错误: {e}")
            raise


class DependencyContainer:
    """依赖容器"""

    def __init__(self):
        self.factory = get_service_factory()

    async def get_db(self) -> AsyncGenerator[AsyncSession, None]:
        """数据库会话依赖"""
        async_session_factory = DatabaseManager.get_async_session_factory()
        async with async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                structured_logger.error(f"数据库会话异常: {e}")
                raise
            finally:
                await session.close()

    async def get_cache(self) -> CacheManager:
        """缓存管理器依赖"""
        return CacheManager.get_instance()

    def get_service_dependency(self, service_type: Type[T]):
        """服务依赖注入装饰器"""
        def dependency(session: AsyncSession = Depends(self.get_db)) -> T:
            return self.factory.get_service(service_type, session)
        return dependency

    def get_repository_dependency(self, repository_type: Type[T], model_class=None):
        """仓储依赖注入装饰器"""
        def dependency(session: AsyncSession = Depends(self.get_db)) -> T:
            return self.factory.get_repository(repository_type, session, model_class)
        return dependency


# 全局依赖容器实例
container = DependencyContainer()

# 便捷的依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """数据库会话依赖"""
    async for session in container.get_db():
        yield session


async def get_cache() -> CacheManager:
    """缓存管理器依赖"""
    return await container.get_cache()


def get_service(service_type: Type[T]):
    """服务依赖注入装饰器"""
    return container.get_service_dependency(service_type)


def get_repository(repository_type: Type[T], model_class=None):
    """仓储依赖注入装饰器"""
    return container.get_repository_dependency(repository_type, model_class)


def register_service(service_type: Type[T], service_class: Type[T]):
    """注册服务"""
    container.factory.register_service(service_type, service_class)


def register_repository(repository_type: Type[T], repository_class: Type[T]):
    """注册仓储"""
    container.factory.register_repository(repository_type, repository_class)


def register_singleton(service_type: Type[T], instance: T):
    """注册单例服务"""
    container.factory.register_singleton(service_type, instance)


# 自动注册装饰器
def service(service_type: Optional[Type] = None):
    """服务注册装饰器"""
    def decorator(cls):
        register_service(service_type or cls, cls)
        return cls
    return decorator


def repository(repository_type: Optional[Type] = None):
    """仓储注册装饰器"""
    def decorator(cls):
        register_repository(repository_type or cls, cls)
        return cls
    return decorator


def singleton(service_type: Optional[Type] = None):
    """单例注册装饰器"""
    def decorator(cls):
        instance = cls()
        register_singleton(service_type or cls, instance)
        return cls
    return decorator
