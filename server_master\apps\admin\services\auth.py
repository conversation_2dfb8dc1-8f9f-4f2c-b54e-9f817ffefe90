"""
认证服务模块
"""
import uuid
import base64
from io import BytesIO
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from core.dependencies import service, get_db
from core.security.jwt_manager import JWTManager
from core.security.password import PasswordSecurity
from core.cache.cache_manager import CacheManager
from core.exceptions import AuthenticationException, BusinessException, ErrorCode
from core.logging.structured_logger import structured_logger, log_execution_time

from .user import UserService
from ..schemas.auth import (
    LoginSchema, RegisterSchema, TokenSchema, UserInfoSchema,
    PasswordChangeSchema, CaptchaSchema, OnlineUserSchema
)
from ..schemas.user import CurrentUserSchema


# HTTP Bearer认证
security = HTTPBearer()


@service()
class AuthService:
    """认证服务类"""

    def __init__(self, user_service: UserService, jwt_manager: JWTManager, cache_manager: CacheManager):
        self.user_service = user_service
        self.jwt_manager = jwt_manager
        self.cache_manager = cache_manager
        self.captcha_enabled = True  # 是否启用验证码
        self.max_login_attempts = 5  # 最大登录尝试次数
        self.lockout_duration = 1800  # 锁定时间（秒）

    @log_execution_time("用户登录")
    async def login(self, login_data: LoginSchema, request: Request) -> TokenSchema:
        """用户登录"""
        try:
            # 获取客户端IP
            client_ip = self._get_client_ip(request)
            
            # 验证验证码
            if self.captcha_enabled and login_data.code:
                await self._verify_captcha(login_data.code, login_data.uuid)

            # 检查账户锁定状态
            await self._check_account_lockout(login_data.username, client_ip)

            # 验证用户凭据
            user = await self._authenticate_user(login_data.username, login_data.password)
            
            # 检查用户状态
            if user.status != '0':
                await self._record_login_attempt(login_data.username, client_ip, False, "账户已停用")
                raise AuthenticationException(ErrorCode.ACCOUNT_DISABLED, "账户已停用")

            # 生成令牌
            access_token = await self.jwt_manager.create_access_token({
                'user_id': user.user_id,
                'username': user.user_name,
                'dept_id': user.dept_id
            })
            
            refresh_token = await self.jwt_manager.create_refresh_token(user.user_id)

            # 更新用户登录信息
            await self.user_service.update_login_info(user.user_id, client_ip)

            # 记录成功登录
            await self._record_login_attempt(login_data.username, client_ip, True, "登录成功")

            # 清除登录失败记录
            await self._clear_login_attempts(login_data.username, client_ip)

            # 缓存在线用户信息
            await self._cache_online_user(user, access_token, client_ip, request)

            structured_logger.info(f"用户登录成功: {user.user_name}", user_id=user.user_id, client_ip=client_ip)

            return TokenSchema(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type='bearer',
                expires_in=1800  # 30分钟
            )

        except AuthenticationException:
            # 记录登录失败
            await self._record_login_attempt(login_data.username, client_ip, False, "用户名或密码错误")
            raise
        except Exception as e:
            structured_logger.error(f"用户登录失败: {e}")
            raise AuthenticationException(ErrorCode.LOGIN_FAILED, "登录失败")

    async def logout(self, token: str) -> bool:
        """用户登出"""
        try:
            # 将令牌加入黑名单
            await self.jwt_manager.revoke_token(token)
            
            # 从在线用户缓存中移除
            payload = await self.jwt_manager.verify_token(token)
            if payload:
                jti = payload.get('jti')
                await self.cache_manager.delete(f"online_user:{jti}")

            structured_logger.info("用户登出成功")
            return True

        except Exception as e:
            structured_logger.error(f"用户登出失败: {e}")
            return False

    async def refresh_token(self, refresh_token: str) -> TokenSchema:
        """刷新访问令牌"""
        try:
            token_data = await self.jwt_manager.refresh_access_token(refresh_token)
            
            return TokenSchema(
                access_token=token_data['access_token'],
                refresh_token=refresh_token,
                token_type='bearer',
                expires_in=1800
            )

        except Exception as e:
            structured_logger.error(f"刷新令牌失败: {e}")
            raise AuthenticationException(ErrorCode.TOKEN_INVALID, "刷新令牌失败")

    async def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> CurrentUserSchema:
        """获取当前用户"""
        try:
            token = credentials.credentials
            payload = await self.jwt_manager.verify_token(token)
            
            if not payload:
                raise AuthenticationException(ErrorCode.TOKEN_INVALID, "无效的访问令牌")

            user_id = payload.get('user_id')
            if not user_id:
                raise AuthenticationException(ErrorCode.TOKEN_INVALID, "令牌中缺少用户信息")

            # 获取用户详情
            user = await self.user_service.get_user_detail(user_id)
            if not user:
                raise AuthenticationException(ErrorCode.UNAUTHORIZED, "用户不存在")

            # 构建当前用户信息
            current_user = CurrentUserSchema(
                user_id=user.user_id,
                user_name=user.user_name,
                nick_name=user.nick_name,
                dept_id=user.dept_id,
                admin=user.admin,
                permissions=[],  # 需要从权限服务获取
                roles=[]  # 需要从角色服务获取
            )

            return current_user

        except AuthenticationException:
            raise
        except Exception as e:
            structured_logger.error(f"获取当前用户失败: {e}")
            raise AuthenticationException(ErrorCode.UNAUTHORIZED, "认证失败")

    async def get_user_info(self, user_id: int) -> UserInfoSchema:
        """获取用户信息"""
        try:
            user = await self.user_service.get_user_detail(user_id)
            if not user:
                raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, "用户不存在")

            return UserInfoSchema(
                user_id=user.user_id,
                user_name=user.user_name,
                nick_name=user.nick_name,
                avatar=user.avatar,
                email=user.email,
                phonenumber=user.phonenumber,
                sex=user.sex,
                dept_id=user.dept_id,
                dept_name=user.dept_name,
                roles=[],  # 需要从角色服务获取
                permissions=[]  # 需要从权限服务获取
            )

        except Exception as e:
            structured_logger.error(f"获取用户信息失败: {e}")
            raise

    async def change_password(self, user_id: int, password_data: PasswordChangeSchema) -> bool:
        """修改密码"""
        try:
            # 验证两次密码是否一致
            password_data.validate_passwords_match()

            # 获取用户信息
            user = await self.user_service.user_repo.get_by_id(user_id)
            if not user:
                raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, "用户不存在")

            # 验证旧密码
            if not PasswordSecurity.verify_password(password_data.old_password, user.salt, user.password):
                raise BusinessException(ErrorCode.VALIDATION_ERROR, "旧密码不正确")

            # 生成新密码哈希
            new_salt = PasswordSecurity.generate_salt()
            new_password_hash = PasswordSecurity.hash_password(password_data.new_password, new_salt)

            # 更新密码
            success = await self.user_service.user_repo.update(user_id, {
                'password': new_password_hash,
                'salt': new_salt
            })

            if success:
                # 撤销用户的所有令牌
                await self.jwt_manager.revoke_all_user_tokens(user_id)
                structured_logger.info(f"用户密码修改成功", user_id=user_id)

            return bool(success)

        except Exception as e:
            structured_logger.error(f"修改密码失败: {e}")
            raise

    async def generate_captcha(self) -> CaptchaSchema:
        """生成验证码"""
        try:
            # 生成验证码UUID
            captcha_uuid = str(uuid.uuid4())
            
            # 生成验证码文本（这里简化为4位数字）
            import random
            captcha_text = ''.join([str(random.randint(0, 9)) for _ in range(4)])
            
            # 缓存验证码（5分钟过期）
            await self.cache_manager.set(f"captcha:{captcha_uuid}", captcha_text, 300)
            
            # 生成验证码图片（这里简化为Base64字符串）
            captcha_img = self._generate_captcha_image(captcha_text)

            return CaptchaSchema(
                uuid=captcha_uuid,
                img=captcha_img,
                captcha_enabled=self.captcha_enabled
            )

        except Exception as e:
            structured_logger.error(f"生成验证码失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "生成验证码失败")

    async def _authenticate_user(self, username: str, password: str):
        """验证用户凭据"""
        user = await self.user_service.get_by_username(username)
        if not user:
            raise AuthenticationException(ErrorCode.LOGIN_FAILED, "用户名或密码错误")

        # 获取完整用户信息（包含密码和盐值）
        user_model = await self.user_service.user_repo.get_by_username(username)
        if not user_model:
            raise AuthenticationException(ErrorCode.LOGIN_FAILED, "用户名或密码错误")

        # 验证密码
        if not PasswordSecurity.verify_password(password, user_model.salt, user_model.password):
            raise AuthenticationException(ErrorCode.LOGIN_FAILED, "用户名或密码错误")

        return user_model

    async def _verify_captcha(self, code: str, uuid: str):
        """验证验证码"""
        if not uuid:
            raise AuthenticationException(ErrorCode.VALIDATION_ERROR, "验证码UUID不能为空")

        cached_code = await self.cache_manager.get(f"captcha:{uuid}")
        if not cached_code:
            raise AuthenticationException(ErrorCode.VALIDATION_ERROR, "验证码已过期")

        if code.lower() != cached_code.lower():
            raise AuthenticationException(ErrorCode.VALIDATION_ERROR, "验证码错误")

        # 删除已使用的验证码
        await self.cache_manager.delete(f"captcha:{uuid}")

    async def _check_account_lockout(self, username: str, client_ip: str):
        """检查账户锁定状态"""
        lockout_key = f"lockout:{username}:{client_ip}"
        lockout_info = await self.cache_manager.get(lockout_key)
        
        if lockout_info:
            raise AuthenticationException(ErrorCode.ACCOUNT_LOCKED, "账户已被锁定，请稍后再试")

    async def _record_login_attempt(self, username: str, client_ip: str, success: bool, message: str):
        """记录登录尝试"""
        if not success:
            # 记录失败次数
            attempt_key = f"login_attempts:{username}:{client_ip}"
            attempts = await self.cache_manager.get(attempt_key) or 0
            attempts += 1
            
            await self.cache_manager.set(attempt_key, attempts, self.lockout_duration)
            
            # 达到最大尝试次数时锁定账户
            if attempts >= self.max_login_attempts:
                lockout_key = f"lockout:{username}:{client_ip}"
                await self.cache_manager.set(lockout_key, True, self.lockout_duration)

        # 这里可以记录到登录日志表
        structured_logger.info(f"登录尝试记录", username=username, client_ip=client_ip, success=success, message=message)

    async def _clear_login_attempts(self, username: str, client_ip: str):
        """清除登录失败记录"""
        attempt_key = f"login_attempts:{username}:{client_ip}"
        await self.cache_manager.delete(attempt_key)

    async def _cache_online_user(self, user, token: str, client_ip: str, request: Request):
        """缓存在线用户信息"""
        # 解析令牌获取JTI
        payload = await self.jwt_manager.verify_token(token)
        jti = payload.get('jti')
        
        online_user = {
            'token_id': jti,
            'user_name': user.user_name,
            'dept_name': None,  # 需要查询部门信息
            'ipaddr': client_ip,
            'login_location': None,  # 可以通过IP获取地理位置
            'browser': request.headers.get('user-agent', ''),
            'os': None,  # 可以从user-agent解析
            'login_time': datetime.now().isoformat()
        }
        
        await self.cache_manager.set(f"online_user:{jti}", online_user, 1800)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        if request.client:
            return request.client.host
        
        return 'unknown'

    def _generate_captcha_image(self, text: str) -> str:
        """生成验证码图片（简化版本）"""
        # 这里应该使用PIL或其他图像库生成真实的验证码图片
        # 为了简化，这里返回一个Base64编码的占位符
        placeholder = f"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        return placeholder
