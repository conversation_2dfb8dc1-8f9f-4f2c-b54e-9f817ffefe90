# 🎉 MXTT-FastAPI 项目迁移完成！

## 迁移说明

项目已成功从 `server/server_master` 迁移到根目录的 `server_master`。

## 迁移内容

✅ **核心框架模块**
- `core/` - 完整的核心框架，包括数据库、缓存、安全、日志、监控等
- `config/` - 统一配置管理
- `common/` - 通用工具模块

✅ **业务应用模块**
- `apps/admin/` - 管理后台模块（用户、认证等）
- `apps/generator/` - 代码生成器模块
- `apps/scheduler/` - 任务调度器模块

✅ **测试模块**
- `tests/unit/` - 单元测试
- `tests/integration/` - 集成测试
- `tests/performance/` - 性能测试

✅ **部署配置**
- `Dockerfile` & `Dockerfile.dev` - Docker镜像构建
- `docker-compose.yml` & `docker-compose.dev.yml` - 服务编排
- `scripts/` - 部署和构建脚本

✅ **项目文件**
- `main.py` - 应用入口
- `requirements.txt` - 依赖管理
- `README.md` - 项目文档
- `DEPLOYMENT.md` - 部署指南

## 快速启动

### 1. 环境配置

```bash
# 复制环境配置文件
cp .env.development.example .env.development

# 编辑配置文件（根据实际情况修改数据库和Redis配置）
# 编辑 .env.development
```

### 2. 开发环境启动

#### 方式一：Docker 启动（推荐）

```bash
# 进入项目目录
cd server_master

# 启动开发环境
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev
```

#### 方式二：本地启动

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 启动应用
python main.py
```

### 3. 访问应用

- **应用地址**: http://localhost:9099
- **API文档**: http://localhost:9099/docs
- **健康检查**: http://localhost:9099/health
- **系统指标**: http://localhost:9099/metrics

### 4. 开发环境服务（Docker模式）

- **数据库管理**: http://localhost:8080 (phpMyAdmin)
- **Redis管理**: http://localhost:8081 (Redis Commander)

## 生产环境部署

```bash
# 配置生产环境
cp .env.production.example .env.production
# 编辑 .env.production，设置安全的密码和密钥

# 启动生产环境
./scripts/deploy.sh prod --build
```

### 生产环境服务

- **应用地址**: http://localhost:9099
- **Nginx代理**: http://localhost
- **监控面板**: http://localhost:3000 (Grafana)
- **指标收集**: http://localhost:9090 (Prometheus)

## 项目特点

🏗️ **企业级架构**
- 分层架构设计（Controller → Service → Repository → Model）
- 依赖注入系统
- 标准化异常处理

🚀 **高性能**
- 全异步编程
- 数据库连接池优化
- 多级缓存策略
- 查询优化

🔒 **高安全性**
- JWT认证机制
- RBAC权限控制
- API限流保护
- 安全头防护

📊 **可观测性**
- 结构化日志
- 性能监控
- 健康检查
- 指标收集

🧪 **测试完善**
- 单元测试
- 集成测试
- 性能测试
- 测试覆盖率

🐳 **容器化部署**
- Docker多阶段构建
- Docker Compose编排
- 开发/生产环境分离
- 自动化部署脚本

## 开发指南

### 添加新业务模块

1. 在 `apps/` 下创建新模块目录
2. 按照分层架构创建 `models/`、`schemas/`、`repositories/`、`services/`、`controllers/`
3. 在 `core/routing.py` 中注册路由
4. 编写对应的测试用例

### 配置管理

- 开发环境：`.env.development`
- 测试环境：`.env.testing`
- 生产环境：`.env.production`

### 日志使用

```python
from core.logging.structured_logger import structured_logger

structured_logger.info("操作成功", user_id=123, action="create")
```

### 缓存使用

```python
from core.cache.cache_manager import cache_result

@cache_result("user_list", ttl=300)
async def get_user_list():
    pass
```

## 故障排除

### 常见问题

1. **端口占用**：检查 9099 端口是否被占用
2. **数据库连接失败**：检查数据库配置和服务状态
3. **Redis连接失败**：检查Redis配置和服务状态
4. **Docker启动失败**：检查Docker服务状态和镜像构建

### 查看日志

```bash
# Docker环境
./scripts/deploy.sh dev --logs

# 本地环境
tail -f logs/app.log
```

## 下一步

1. 根据业务需求添加新的功能模块
2. 完善测试用例，提高测试覆盖率
3. 配置CI/CD流水线
4. 部署到生产环境

---

**注意**: 请确保在生产环境中使用强密码和安全配置，定期更新系统和依赖包。

迁移完成时间：2024-12-23
项目版本：v2.0.0
