"""
代码生成器服务
"""
import os
import zipfile
from typing import List, Dict, Any, Optional
from jinja2 import Environment, FileSystemLoader
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from core.base.service import BusinessService
from core.dependencies import service
from core.exceptions import BusinessException, ErrorCode
from core.logging.structured_logger import structured_logger, log_execution_time
from config import settings

from ..models.table import GenTableModel, GenTableColumnModel


@service()
class GeneratorService(BusinessService):
    """代码生成器服务"""

    def __init__(self, session: AsyncSession):
        super().__init__(session)
        self.template_path = settings.app.generator_template_path
        self.output_path = settings.app.generator_output_path
        self.jinja_env = Environment(loader=FileSystemLoader(self.template_path))

    @log_execution_time("获取数据库表列表")
    async def get_database_tables(self, table_name: Optional[str] = None, table_comment: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取数据库表列表"""
        try:
            sql = """
            SELECT 
                table_name,
                table_comment,
                create_time,
                update_time
            FROM information_schema.tables 
            WHERE table_schema = (SELECT DATABASE())
            AND table_type = 'BASE TABLE'
            """
            
            params = {}
            conditions = []
            
            if table_name:
                conditions.append("AND table_name LIKE :table_name")
                params['table_name'] = f"%{table_name}%"
            
            if table_comment:
                conditions.append("AND table_comment LIKE :table_comment")
                params['table_comment'] = f"%{table_comment}%"
            
            if conditions:
                sql += " " + " ".join(conditions)
            
            sql += " ORDER BY create_time DESC"
            
            result = await self.session.execute(text(sql), params)
            tables = []
            
            for row in result.fetchall():
                tables.append({
                    'table_name': row.table_name,
                    'table_comment': row.table_comment or '',
                    'create_time': row.create_time,
                    'update_time': row.update_time
                })
            
            return tables
            
        except Exception as e:
            structured_logger.error(f"获取数据库表列表失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取数据库表列表失败")

    @log_execution_time("获取表字段信息")
    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表字段信息"""
        try:
            sql = """
            SELECT 
                column_name,
                column_default,
                is_nullable,
                data_type,
                character_maximum_length,
                numeric_precision,
                numeric_scale,
                column_comment,
                column_key,
                extra
            FROM information_schema.columns 
            WHERE table_schema = (SELECT DATABASE())
            AND table_name = :table_name
            ORDER BY ordinal_position
            """
            
            result = await self.session.execute(text(sql), {'table_name': table_name})
            columns = []
            
            for row in result.fetchall():
                columns.append({
                    'column_name': row.column_name,
                    'column_default': row.column_default,
                    'is_nullable': row.is_nullable,
                    'data_type': row.data_type,
                    'character_maximum_length': row.character_maximum_length,
                    'numeric_precision': row.numeric_precision,
                    'numeric_scale': row.numeric_scale,
                    'column_comment': row.column_comment or '',
                    'column_key': row.column_key,
                    'extra': row.extra,
                    'is_pk': '1' if row.column_key == 'PRI' else '0',
                    'is_increment': '1' if 'auto_increment' in (row.extra or '') else '0',
                    'java_type': self._convert_to_java_type(row.data_type),
                    'java_field': self._convert_to_camel_case(row.column_name),
                    'html_type': self._get_html_type(row.data_type)
                })
            
            return columns
            
        except Exception as e:
            structured_logger.error(f"获取表字段信息失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取表字段信息失败")

    @log_execution_time("生成代码")
    async def generate_code(self, table_names: List[str], template_type: str = 'crud') -> str:
        """生成代码"""
        try:
            # 创建输出目录
            output_dir = os.path.join(self.output_path, f"generated_{int(time.time())}")
            os.makedirs(output_dir, exist_ok=True)
            
            for table_name in table_names:
                # 获取表信息
                table_info = await self._get_table_info(table_name)
                columns = await self.get_table_columns(table_name)
                
                # 生成代码文件
                await self._generate_model_file(table_info, columns, output_dir)
                await self._generate_schema_file(table_info, columns, output_dir)
                await self._generate_repository_file(table_info, columns, output_dir)
                await self._generate_service_file(table_info, columns, output_dir)
                await self._generate_controller_file(table_info, columns, output_dir)
            
            # 打包成zip文件
            zip_path = f"{output_dir}.zip"
            await self._create_zip_file(output_dir, zip_path)
            
            structured_logger.info(f"代码生成成功: {zip_path}")
            return zip_path
            
        except Exception as e:
            structured_logger.error(f"代码生成失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "代码生成失败")

    async def _get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表基本信息"""
        sql = """
        SELECT table_name, table_comment
        FROM information_schema.tables 
        WHERE table_schema = (SELECT DATABASE())
        AND table_name = :table_name
        """
        
        result = await self.session.execute(text(sql), {'table_name': table_name})
        row = result.fetchone()
        
        if not row:
            raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, f"表不存在: {table_name}")
        
        return {
            'table_name': row.table_name,
            'table_comment': row.table_comment or '',
            'class_name': self._convert_to_class_name(table_name),
            'module_name': table_name.replace('_', ''),
            'business_name': table_name.replace('_', ''),
            'function_name': row.table_comment or table_name
        }

    async def _generate_model_file(self, table_info: Dict, columns: List[Dict], output_dir: str):
        """生成模型文件"""
        template = self.jinja_env.get_template('model.py.j2')
        content = template.render(table=table_info, columns=columns)
        
        file_path = os.path.join(output_dir, f"{table_info['module_name']}_model.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    async def _generate_schema_file(self, table_info: Dict, columns: List[Dict], output_dir: str):
        """生成Schema文件"""
        template = self.jinja_env.get_template('schema.py.j2')
        content = template.render(table=table_info, columns=columns)
        
        file_path = os.path.join(output_dir, f"{table_info['module_name']}_schema.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    async def _generate_repository_file(self, table_info: Dict, columns: List[Dict], output_dir: str):
        """生成Repository文件"""
        template = self.jinja_env.get_template('repository.py.j2')
        content = template.render(table=table_info, columns=columns)
        
        file_path = os.path.join(output_dir, f"{table_info['module_name']}_repository.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    async def _generate_service_file(self, table_info: Dict, columns: List[Dict], output_dir: str):
        """生成Service文件"""
        template = self.jinja_env.get_template('service.py.j2')
        content = template.render(table=table_info, columns=columns)
        
        file_path = os.path.join(output_dir, f"{table_info['module_name']}_service.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    async def _generate_controller_file(self, table_info: Dict, columns: List[Dict], output_dir: str):
        """生成Controller文件"""
        template = self.jinja_env.get_template('controller.py.j2')
        content = template.render(table=table_info, columns=columns)
        
        file_path = os.path.join(output_dir, f"{table_info['module_name']}_controller.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    async def _create_zip_file(self, source_dir: str, zip_path: str):
        """创建zip文件"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arcname)

    def _convert_to_java_type(self, mysql_type: str) -> str:
        """转换MySQL类型到Python类型"""
        type_mapping = {
            'int': 'int',
            'bigint': 'int',
            'tinyint': 'int',
            'smallint': 'int',
            'mediumint': 'int',
            'varchar': 'str',
            'char': 'str',
            'text': 'str',
            'longtext': 'str',
            'datetime': 'datetime',
            'timestamp': 'datetime',
            'date': 'date',
            'time': 'time',
            'decimal': 'Decimal',
            'float': 'float',
            'double': 'float',
            'json': 'dict'
        }
        return type_mapping.get(mysql_type.lower(), 'str')

    def _convert_to_camel_case(self, snake_str: str) -> str:
        """转换下划线命名到驼峰命名"""
        components = snake_str.split('_')
        return components[0] + ''.join(x.capitalize() for x in components[1:])

    def _convert_to_class_name(self, table_name: str) -> str:
        """转换表名到类名"""
        # 移除前缀
        if table_name.startswith('sys_'):
            table_name = table_name[4:]
        elif table_name.startswith('gen_'):
            table_name = table_name[4:]
        
        # 转换为驼峰命名
        components = table_name.split('_')
        return ''.join(x.capitalize() for x in components)

    def _get_html_type(self, mysql_type: str) -> str:
        """获取HTML控件类型"""
        type_mapping = {
            'int': 'input',
            'bigint': 'input',
            'varchar': 'input',
            'char': 'input',
            'text': 'textarea',
            'longtext': 'textarea',
            'datetime': 'datetime',
            'date': 'date',
            'time': 'time'
        }
        return type_mapping.get(mysql_type.lower(), 'input')
