"""
查询优化器模块
"""
import time
from typing import List, Dict, Any, Optional, Type
from sqlalchemy import select, func, and_, or_, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import Select

from ..logging.structured_logger import structured_logger
from .database_manager import db_metrics


class QueryOptimizer:
    """查询优化器"""

    @staticmethod
    async def paginate_with_cursor(
        db: AsyncSession,
        query: Select,
        cursor_field: str,
        cursor: Optional[Any] = None,
        limit: int = 20,
        order_desc: bool = True
    ) -> Dict[str, Any]:
        """基于游标的分页查询 - 适用于大数据量场景"""
        start_time = time.time()
        
        try:
            # 获取模型类
            model_class = query.column_descriptions[0]['entity']
            cursor_column = getattr(model_class, cursor_field)
            
            if cursor:
                if order_desc:
                    query = query.where(cursor_column < cursor)
                else:
                    query = query.where(cursor_column > cursor)

            # 多查询一条判断是否有下一页
            query = query.limit(limit + 1)
            if order_desc:
                query = query.order_by(cursor_column.desc())
            else:
                query = query.order_by(cursor_column)

            result = await db.execute(query)
            items = result.scalars().all()

            has_next = len(items) > limit
            if has_next:
                items = items[:-1]

            next_cursor = getattr(items[-1], cursor_field) if items and has_next else None

            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_slow=execution_time > 1.0)

            structured_logger.debug(
                "游标分页查询完成",
                cursor_field=cursor_field,
                limit=limit,
                has_next=has_next,
                execution_time=execution_time
            )

            return {
                'items': items,
                'has_next': has_next,
                'next_cursor': next_cursor,
                'limit': limit
            }
        except Exception as e:
            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_error=True)
            structured_logger.error(f"游标分页查询失败: {e}")
            raise

    @staticmethod
    async def optimized_paginate(
        db: AsyncSession,
        query: Select,
        page_num: int,
        page_size: int,
        count_query: Optional[Select] = None
    ) -> Dict[str, Any]:
        """优化的分页查询 - 避免重复计算总数"""
        start_time = time.time()
        
        try:
            # 如果提供了专门的计数查询，使用它
            if count_query:
                total_result = await db.execute(count_query)
                total = total_result.scalar()
            else:
                # 使用子查询计算总数
                count_query = select(func.count()).select_from(query.subquery())
                total_result = await db.execute(count_query)
                total = total_result.scalar()

            # 分页查询
            offset = (page_num - 1) * page_size
            paginated_query = query.offset(offset).limit(page_size)
            result = await db.execute(paginated_query)
            items = result.scalars().all()

            # 计算分页信息
            total_pages = (total + page_size - 1) // page_size
            has_next = page_num < total_pages
            has_prev = page_num > 1

            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_slow=execution_time > 1.0)

            structured_logger.debug(
                "优化分页查询完成",
                page_num=page_num,
                page_size=page_size,
                total=total,
                execution_time=execution_time
            )

            return {
                'items': items,
                'total': total,
                'page_num': page_num,
                'page_size': page_size,
                'total_pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev
            }
        except Exception as e:
            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_error=True)
            structured_logger.error(f"优化分页查询失败: {e}")
            raise


class BatchOperationService:
    """批量操作服务"""

    @staticmethod
    async def bulk_insert(
        db: AsyncSession, 
        model_class: Type, 
        data_list: List[dict], 
        batch_size: int = 1000
    ) -> int:
        """批量插入优化"""
        if not data_list:
            return 0

        start_time = time.time()
        total_inserted = 0

        try:
            # 分批处理大量数据
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                
                # 使用bulk_insert_mappings提高性能
                await db.execute(
                    model_class.__table__.insert(),
                    batch
                )
                total_inserted += len(batch)

            await db.flush()

            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_slow=execution_time > 2.0)

            structured_logger.info(
                "批量插入完成",
                model=model_class.__name__,
                total_count=total_inserted,
                batch_size=batch_size,
                execution_time=execution_time
            )

            return total_inserted

        except Exception as e:
            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_error=True)
            structured_logger.error(f"批量插入失败: {e}")
            raise

    @staticmethod
    async def bulk_update(
        db: AsyncSession, 
        model_class: Type, 
        updates: List[Dict[str, Any]], 
        batch_size: int = 1000
    ) -> int:
        """批量更新优化"""
        if not updates:
            return 0

        start_time = time.time()
        updated_count = 0

        try:
            # 分批处理
            for i in range(0, len(updates), batch_size):
                batch = updates[i:i + batch_size]
                
                for update_data in batch:
                    id_value = update_data.pop('id')
                    result = await db.execute(
                        update(model_class)
                        .where(model_class.id == id_value)
                        .values(**update_data)
                    )
                    updated_count += result.rowcount

            await db.flush()

            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_slow=execution_time > 2.0)

            structured_logger.info(
                "批量更新完成",
                model=model_class.__name__,
                updated_count=updated_count,
                batch_size=batch_size,
                execution_time=execution_time
            )

            return updated_count

        except Exception as e:
            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_error=True)
            structured_logger.error(f"批量更新失败: {e}")
            raise

    @staticmethod
    async def bulk_delete(
        db: AsyncSession, 
        model_class: Type, 
        ids: List[Any], 
        batch_size: int = 1000
    ) -> int:
        """批量删除优化"""
        if not ids:
            return 0

        start_time = time.time()
        deleted_count = 0

        try:
            # 分批处理
            for i in range(0, len(ids), batch_size):
                batch_ids = ids[i:i + batch_size]
                
                result = await db.execute(
                    delete(model_class).where(model_class.id.in_(batch_ids))
                )
                deleted_count += result.rowcount

            await db.flush()

            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_slow=execution_time > 2.0)

            structured_logger.info(
                "批量删除完成",
                model=model_class.__name__,
                deleted_count=deleted_count,
                batch_size=batch_size,
                execution_time=execution_time
            )

            return deleted_count

        except Exception as e:
            execution_time = time.time() - start_time
            db_metrics.record_query(execution_time, is_error=True)
            structured_logger.error(f"批量删除失败: {e}")
            raise


class EagerLoadingService:
    """预加载服务 - 解决N+1查询问题"""

    @staticmethod
    def with_relationships(query: Select, *relationships) -> Select:
        """添加关系预加载"""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(selectinload(relationship))
            else:
                query = query.options(relationship)
        return query

    @staticmethod
    def with_joined_load(query: Select, *relationships) -> Select:
        """使用JOIN预加载 - 适用于一对一关系"""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(joinedload(relationship))
            else:
                query = query.options(relationship)
        return query

    @staticmethod
    def with_select_in_load(query: Select, *relationships) -> Select:
        """使用SELECT IN预加载 - 适用于一对多关系"""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(selectinload(relationship))
            else:
                query = query.options(relationship)
        return query


class QueryBuilder:
    """查询构建器"""

    def __init__(self, model_class: Type):
        self.model_class = model_class
        self.query = select(model_class)

    def filter_by(self, **kwargs) -> 'QueryBuilder':
        """按条件过滤"""
        conditions = []
        for key, value in kwargs.items():
            if hasattr(self.model_class, key):
                if isinstance(value, list):
                    conditions.append(getattr(self.model_class, key).in_(value))
                elif isinstance(value, dict):
                    # 支持范围查询
                    field = getattr(self.model_class, key)
                    if 'gte' in value:
                        conditions.append(field >= value['gte'])
                    if 'lte' in value:
                        conditions.append(field <= value['lte'])
                    if 'gt' in value:
                        conditions.append(field > value['gt'])
                    if 'lt' in value:
                        conditions.append(field < value['lt'])
                    if 'like' in value:
                        conditions.append(field.like(f"%{value['like']}%"))
                else:
                    conditions.append(getattr(self.model_class, key) == value)
        
        if conditions:
            self.query = self.query.where(and_(*conditions))
        return self

    def order_by(self, field: str, desc: bool = False) -> 'QueryBuilder':
        """排序"""
        if hasattr(self.model_class, field):
            column = getattr(self.model_class, field)
            if desc:
                self.query = self.query.order_by(column.desc())
            else:
                self.query = self.query.order_by(column)
        return self

    def limit(self, limit: int) -> 'QueryBuilder':
        """限制数量"""
        self.query = self.query.limit(limit)
        return self

    def offset(self, offset: int) -> 'QueryBuilder':
        """偏移量"""
        self.query = self.query.offset(offset)
        return self

    def with_relationships(self, *relationships) -> 'QueryBuilder':
        """预加载关系"""
        self.query = EagerLoadingService.with_relationships(self.query, *relationships)
        return self

    def build(self) -> Select:
        """构建查询"""
        return self.query
