"""
中间件处理器模块
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from .performance_middleware import (
    PerformanceMonitoringMiddleware,
    RequestContextMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware,
    CompressionMiddleware
)
from ..logging.structured_logger import structured_logger
from ..security.rate_limiter import RateLimiter
from ..cache.cache_manager import CacheManager


def setup_middleware(app: FastAPI, config=None):
    """设置中间件"""
    try:
        # 1. 请求上下文中间件（最先执行）
        app.add_middleware(RequestContextMiddleware)
        structured_logger.info("已添加请求上下文中间件")

        # 2. 性能监控中间件
        app.add_middleware(PerformanceMonitoringMiddleware)
        structured_logger.info("已添加性能监控中间件")

        # 3. 安全头中间件
        app.add_middleware(SecurityHeadersMiddleware)
        structured_logger.info("已添加安全头中间件")

        # 4. 限流中间件
        if config and config.rate_limit_enabled:
            try:
                cache_manager = CacheManager.get_instance()
                rate_limiter = RateLimiter(cache_manager)
                app.add_middleware(RateLimitMiddleware, rate_limiter=rate_limiter)
                structured_logger.info("已添加限流中间件")
            except Exception as e:
                structured_logger.warning(f"限流中间件初始化失败，跳过: {e}")

        # 5. CORS中间件
        if config and config.enable_cors:
            cors_config = config.get_cors_config()
            app.add_middleware(
                CORSMiddleware,
                **cors_config
            )
            structured_logger.info("已添加CORS中间件")

        # 6. 压缩中间件
        if config and config.enable_gzip:
            app.add_middleware(
                GZipMiddleware,
                minimum_size=config.gzip_minimum_size
            )
            structured_logger.info("已添加Gzip压缩中间件")

        # 7. 受信任主机中间件
        if config and config.allowed_hosts and config.allowed_hosts != ["*"]:
            app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=config.allowed_hosts
            )
            structured_logger.info("已添加受信任主机中间件")

        structured_logger.info("所有中间件设置完成")

    except Exception as e:
        structured_logger.error(f"中间件设置失败: {e}")
        raise


def setup_cors_middleware(app: FastAPI, config):
    """单独设置CORS中间件"""
    if not config.enable_cors:
        return

    cors_config = config.get_cors_config()
    app.add_middleware(CORSMiddleware, **cors_config)
    structured_logger.info("CORS中间件设置完成")


def setup_security_middleware(app: FastAPI):
    """设置安全相关中间件"""
    # 安全头中间件
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 可以添加其他安全中间件
    # 例如：CSP、HSTS等
    
    structured_logger.info("安全中间件设置完成")


def setup_monitoring_middleware(app: FastAPI):
    """设置监控相关中间件"""
    # 性能监控中间件
    app.add_middleware(PerformanceMonitoringMiddleware)
    
    # 请求上下文中间件
    app.add_middleware(RequestContextMiddleware)
    
    structured_logger.info("监控中间件设置完成")


class MiddlewareManager:
    """中间件管理器"""

    def __init__(self, app: FastAPI):
        self.app = app
        self.middleware_stack = []

    def add_middleware(self, middleware_class, **kwargs):
        """添加中间件"""
        try:
            self.app.add_middleware(middleware_class, **kwargs)
            self.middleware_stack.append({
                'class': middleware_class.__name__,
                'kwargs': kwargs
            })
            structured_logger.info(f"已添加中间件: {middleware_class.__name__}")
        except Exception as e:
            structured_logger.error(f"添加中间件失败: {middleware_class.__name__}, 错误: {e}")
            raise

    def remove_middleware(self, middleware_class):
        """移除中间件（注意：FastAPI不支持动态移除中间件）"""
        structured_logger.warning("FastAPI不支持动态移除中间件")

    def get_middleware_info(self) -> list:
        """获取中间件信息"""
        return self.middleware_stack.copy()

    def setup_default_middleware(self, config=None):
        """设置默认中间件栈"""
        # 按照执行顺序添加中间件
        middleware_configs = [
            (RequestContextMiddleware, {}),
            (PerformanceMonitoringMiddleware, {}),
            (SecurityHeadersMiddleware, {}),
        ]

        # 根据配置添加可选中间件
        if config:
            if config.enable_cors:
                cors_config = config.get_cors_config()
                middleware_configs.append((CORSMiddleware, cors_config))

            if config.enable_gzip:
                middleware_configs.append((
                    GZipMiddleware, 
                    {'minimum_size': config.gzip_minimum_size}
                ))

            if config.allowed_hosts and config.allowed_hosts != ["*"]:
                middleware_configs.append((
                    TrustedHostMiddleware,
                    {'allowed_hosts': config.allowed_hosts}
                ))

        # 添加所有中间件
        for middleware_class, kwargs in middleware_configs:
            self.add_middleware(middleware_class, **kwargs)

        structured_logger.info(f"默认中间件栈设置完成，共{len(middleware_configs)}个中间件")


# 中间件优先级定义
MIDDLEWARE_PRIORITY = {
    'RequestContextMiddleware': 1,
    'PerformanceMonitoringMiddleware': 2,
    'SecurityHeadersMiddleware': 3,
    'RateLimitMiddleware': 4,
    'CORSMiddleware': 5,
    'GZipMiddleware': 6,
    'TrustedHostMiddleware': 7,
}


def get_middleware_priority(middleware_name: str) -> int:
    """获取中间件优先级"""
    return MIDDLEWARE_PRIORITY.get(middleware_name, 999)
