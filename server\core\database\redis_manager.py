"""
Redis管理器模块
"""
from redis import asyncio as aioredis
from redis.exceptions import AuthenticationError, TimeoutError, RedisError

from config import settings
from .database_manager import DatabaseManager
from ..logging.manager import logger


class RedisManager:
    """Redis相关方法，提供Redis连接管理和初始化功能"""

    @classmethod
    async def create_redis_pool(cls) -> aioredis.Redis:
        """
        应用启动时初始化Redis连接

        :return: Redis连接对象
        """
        logger.info('开始连接Redis...')
        redis_config = settings.redis
        redis = await aioredis.from_url(
            url=f'redis://{redis_config.host}',
            port=redis_config.port,
            username=redis_config.username,
            password=redis_config.password,
            db=redis_config.database,
            encoding='utf-8',
            decode_responses=True,
        )
        try:
            connection = await redis.ping()
            if connection:
                logger.info('Redis连接成功')
            else:
                logger.error('Redis连接失败')
        except AuthenticationError as e:
            logger.error(f'Redis用户名或密码错误，详细错误信息：{e}')
        except TimeoutError as e:
            logger.error(f'Redis连接超时，详细错误信息：{e}')
        except RedisError as e:
            logger.error(f'Redis连接错误，详细错误信息：{e}')
        return redis

    @classmethod
    async def close_redis_pool(cls, app):
        """
        应用关闭时关闭Redis连接

        :param app: FastAPI应用实例
        :return:
        """
        await app.state.redis.close()
        logger.info('关闭Redis连接成功')

    @classmethod
    async def init_sys_dict(cls, redis):
        """
        应用启动时缓存字典表

        :param redis: Redis对象
        :return:
        """
        # 延迟导入避免循环依赖
        from apps.admin.services.dict import DictDataService

        async_session_factory = DatabaseManager.get_async_session_factory()
        async with async_session_factory() as session:
            await DictDataService.init_cache_sys_dict_services(session, redis)

    @classmethod
    async def init_sys_config(cls, redis):
        """
        应用启动时缓存参数配置表

        :param redis: Redis对象
        :return:
        """
        # 延迟导入避免循环依赖
        from apps.admin.services.config import ConfigService

        async_session_factory = DatabaseManager.get_async_session_factory()
        async with async_session_factory() as session:
            await ConfigService.init_cache_sys_config_services(session, redis)