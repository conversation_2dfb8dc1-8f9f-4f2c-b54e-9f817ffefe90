"""
路由注册器模块
"""
from fastapi import FastAPI, APIRouter
from typing import List, Dict, Any

from ..apps.admin.controllers.auth import create_auth_router
from ..apps.admin.controllers.user import create_user_router
from .logging.structured_logger import structured_logger


class RouterRegistry:
    """路由注册器"""

    def __init__(self):
        self.routers: List[APIRouter] = []
        self.router_info: List[Dict[str, Any]] = []

    def register_router(self, router: APIRouter, prefix: str = "", tags: List[str] = None):
        """注册路由器"""
        try:
            self.routers.append(router)
            self.router_info.append({
                'prefix': prefix,
                'tags': tags or [],
                'routes_count': len(router.routes)
            })
            structured_logger.info(f"路由器注册成功: {prefix}, 路由数量: {len(router.routes)}")
        except Exception as e:
            structured_logger.error(f"路由器注册失败: {prefix}, 错误: {e}")
            raise

    def register_all_routers(self, app: FastAPI):
        """注册所有路由器到FastAPI应用"""
        try:
            # 注册认证路由
            auth_router = create_auth_router()
            app.include_router(auth_router, prefix="/api")
            
            # 注册用户管理路由
            user_router = create_user_router()
            if user_router:  # 检查是否成功创建
                app.include_router(user_router, prefix="/api")
            
            # 可以继续添加其他业务模块的路由
            # role_router = create_role_router()
            # app.include_router(role_router, prefix="/api")
            
            # dept_router = create_dept_router()
            # app.include_router(dept_router, prefix="/api")
            
            structured_logger.info("所有路由器注册完成")
            
        except Exception as e:
            structured_logger.error(f"路由器注册失败: {e}")
            raise

    def get_router_info(self) -> List[Dict[str, Any]]:
        """获取路由器信息"""
        return self.router_info.copy()

    def get_routes_summary(self, app: FastAPI) -> Dict[str, Any]:
        """获取路由摘要信息"""
        routes_info = []
        
        for route in app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                routes_info.append({
                    'path': route.path,
                    'methods': list(route.methods),
                    'name': getattr(route, 'name', ''),
                    'tags': getattr(route, 'tags', [])
                })
        
        return {
            'total_routes': len(routes_info),
            'routes': routes_info
        }


# 全局路由注册器实例
router_registry = RouterRegistry()


def setup_routers(app: FastAPI):
    """设置路由器的便捷函数"""
    router_registry.register_all_routers(app)
