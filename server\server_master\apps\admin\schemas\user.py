"""
用户数据传输对象模块
"""
import re
from datetime import datetime
from typing import Optional, List, Union
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from pydantic.alias_generators import to_camel

from core.exceptions import ValidationException


class UserBaseSchema(BaseModel):
    """用户基础模式"""
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True
    )

    user_name: str = Field(..., description='用户账号', max_length=30)
    nick_name: str = Field(..., description='用户昵称', max_length=30)
    email: Optional[str] = Field(default='', description='用户邮箱', max_length=50)
    phonenumber: Optional[str] = Field(default='', description='手机号码', max_length=11)
    sex: Optional[str] = Field(default='0', description='用户性别（0男 1女 2未知）')
    dept_id: Optional[int] = Field(default=None, description='部门ID')
    status: Optional[str] = Field(default='0', description='帐号状态（0正常 1停用）')
    remark: Optional[str] = Field(default=None, description='备注')

    @field_validator('user_name')
    @classmethod
    def validate_user_name(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValidationException('用户账号不能为空')
        if len(v) > 30:
            raise ValidationException('用户账号长度不能超过30个字符')
        # 检查脚本字符
        if re.search(r'[<>"\'/\\]', v):
            raise ValidationException('用户账号不能包含脚本字符')
        return v.strip()

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: Optional[str]) -> Optional[str]:
        if v and v.strip():
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValidationException('邮箱格式不正确')
        return v

    @field_validator('phonenumber')
    @classmethod
    def validate_phonenumber(cls, v: Optional[str]) -> Optional[str]:
        if v and v.strip():
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, v):
                raise ValidationException('手机号码格式不正确')
        return v


class UserCreateSchema(UserBaseSchema):
    """用户创建模式"""
    
    password: str = Field(..., description='用户密码', min_length=6, max_length=20)
    role_ids: Optional[List[int]] = Field(default=[], description='角色ID列表')
    post_ids: Optional[List[int]] = Field(default=[], description='岗位ID列表')

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if not v or len(v) < 6:
            raise ValidationException('密码长度不能少于6个字符')
        if len(v) > 20:
            raise ValidationException('密码长度不能超过20个字符')
        # 检查非法字符
        if re.search(r'[<>"\'|\\]', v):
            raise ValidationException('密码不能包含非法字符：< > " \' \\ |')
        return v


class UserUpdateSchema(UserBaseSchema):
    """用户更新模式"""
    
    user_id: int = Field(..., description='用户ID')
    role_ids: Optional[List[int]] = Field(default=[], description='角色ID列表')
    post_ids: Optional[List[int]] = Field(default=[], description='岗位ID列表')


class UserPasswordUpdateSchema(BaseModel):
    """用户密码更新模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_id: int = Field(..., description='用户ID')
    old_password: str = Field(..., description='旧密码')
    new_password: str = Field(..., description='新密码', min_length=6, max_length=20)

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        if not v or len(v) < 6:
            raise ValidationException('新密码长度不能少于6个字符')
        if len(v) > 20:
            raise ValidationException('新密码长度不能超过20个字符')
        if re.search(r'[<>"\'|\\]', v):
            raise ValidationException('新密码不能包含非法字符：< > " \' \\ |')
        return v


class UserQuerySchema(BaseModel):
    """用户查询模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_name: Optional[str] = Field(default=None, description='用户账号')
    nick_name: Optional[str] = Field(default=None, description='用户昵称')
    email: Optional[str] = Field(default=None, description='用户邮箱')
    phonenumber: Optional[str] = Field(default=None, description='手机号码')
    status: Optional[str] = Field(default=None, description='帐号状态')
    dept_id: Optional[int] = Field(default=None, description='部门ID')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class UserPageQuerySchema(UserQuerySchema):
    """用户分页查询模式"""
    
    page_num: int = Field(default=1, description='页码', ge=1)
    page_size: int = Field(default=10, description='每页数量', ge=1, le=100)


class UserResponseSchema(BaseModel):
    """用户响应模式"""
    
    model_config = ConfigDict(
        alias_generator=to_camel,
        from_attributes=True
    )

    user_id: int = Field(description='用户ID')
    dept_id: Optional[int] = Field(description='部门ID')
    user_name: str = Field(description='用户账号')
    nick_name: str = Field(description='用户昵称')
    user_type: Optional[str] = Field(description='用户类型')
    email: Optional[str] = Field(description='用户邮箱')
    phonenumber: Optional[str] = Field(description='手机号码')
    sex: Optional[str] = Field(description='用户性别')
    avatar: Optional[str] = Field(description='头像地址')
    status: Optional[str] = Field(description='帐号状态')
    login_ip: Optional[str] = Field(description='最后登录IP')
    login_date: Optional[datetime] = Field(description='最后登录时间')
    create_by: Optional[str] = Field(description='创建者')
    create_time: Optional[datetime] = Field(description='创建时间')
    update_by: Optional[str] = Field(description='更新者')
    update_time: Optional[datetime] = Field(description='更新时间')
    remark: Optional[str] = Field(description='备注')
    admin: Optional[bool] = Field(default=False, description='是否为管理员')
    dept_name: Optional[str] = Field(default=None, description='部门名称')
    role_names: Optional[str] = Field(default=None, description='角色名称')

    @model_validator(mode='after')
    def check_admin(self) -> 'UserResponseSchema':
        """检查是否为管理员"""
        if self.user_id == 1:
            self.admin = True
        return self


class UserDetailSchema(UserResponseSchema):
    """用户详情模式"""
    
    roles: Optional[List[dict]] = Field(default=[], description='用户角色列表')
    posts: Optional[List[dict]] = Field(default=[], description='用户岗位列表')


class CurrentUserSchema(BaseModel):
    """当前用户模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_id: int = Field(description='用户ID')
    user_name: str = Field(description='用户账号')
    nick_name: str = Field(description='用户昵称')
    dept_id: Optional[int] = Field(description='部门ID')
    admin: bool = Field(default=False, description='是否为管理员')
    permissions: List[str] = Field(default=[], description='权限列表')
    roles: List[str] = Field(default=[], description='角色列表')


class UserProfileSchema(BaseModel):
    """用户个人信息模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_id: int = Field(description='用户ID')
    nick_name: str = Field(description='用户昵称')
    email: Optional[str] = Field(description='用户邮箱')
    phonenumber: Optional[str] = Field(description='手机号码')
    sex: Optional[str] = Field(description='用户性别')


class UserImportSchema(BaseModel):
    """用户导入模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_name: str = Field(description='用户账号')
    nick_name: str = Field(description='用户昵称')
    email: Optional[str] = Field(default='', description='用户邮箱')
    phonenumber: Optional[str] = Field(default='', description='手机号码')
    sex: Optional[str] = Field(default='0', description='用户性别')
    dept_name: Optional[str] = Field(default='', description='部门名称')
    role_names: Optional[str] = Field(default='', description='角色名称')
    status: Optional[str] = Field(default='0', description='帐号状态')


class UserExportSchema(BaseModel):
    """用户导出模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_id: int = Field(description='用户ID')
    user_name: str = Field(description='用户账号')
    nick_name: str = Field(description='用户昵称')
    email: Optional[str] = Field(description='用户邮箱')
    phonenumber: Optional[str] = Field(description='手机号码')
    sex_label: Optional[str] = Field(description='用户性别')
    dept_name: Optional[str] = Field(description='部门名称')
    status_label: Optional[str] = Field(description='帐号状态')
    create_time: Optional[datetime] = Field(description='创建时间')
