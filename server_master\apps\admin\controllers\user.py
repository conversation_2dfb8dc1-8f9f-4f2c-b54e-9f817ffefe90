"""
用户控制器模块
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession

from core.base.controller import CRUDController
from core.dependencies import get_db, get_service
from core.security.permission_manager import require_permission, PermissionLevel, ResourceType
from core.logging.structured_logger import log_api_call
from core.exceptions import BusinessException, ErrorCode

from ..services.user import UserService
from ..schemas.user import (
    UserCreateSchema, UserUpdateSchema, UserQuerySchema, UserResponseSchema,
    UserDetailSchema, UserPasswordUpdateSchema, UserProfileSchema, UserPageQuerySchema
)


class UserController(CRUDController):
    """用户控制器"""

    def __init__(self, user_service: UserService):
        super().__init__(user_service)

    def _to_response_schema(self, obj) -> UserResponseSchema:
        """转换为响应模式"""
        return UserResponseSchema.model_validate(obj)

    @log_api_call("创建用户")
    @require_permission(ResourceType.USER, PermissionLevel.WRITE)
    async def create(self, user_data: UserCreateSchema, current_user=None) -> UserResponseSchema:
        """创建用户"""
        try:
            result = await self.service.create_user(user_data, current_user.user_name)
            return result
        except Exception as e:
            self._handle_exception(e, "create_user")

    @log_api_call("更新用户")
    @require_permission(ResourceType.USER, PermissionLevel.WRITE)
    async def update(self, user_id: int, user_data: UserUpdateSchema, current_user=None) -> UserResponseSchema:
        """更新用户"""
        try:
            user_data.user_id = user_id
            result = await self.service.update_user(user_data, current_user.user_name)
            return result
        except Exception as e:
            self._handle_exception(e, f"update_user({user_id})")

    @log_api_call("删除用户")
    @require_permission(ResourceType.USER, PermissionLevel.DELETE)
    async def delete(self, user_id: int, current_user=None) -> Dict[str, Any]:
        """删除用户"""
        try:
            success = await self.service.delete_user(user_id, current_user.user_name)
            if success:
                return {"success": True, "message": "用户删除成功"}
            else:
                raise BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "用户删除失败")
        except Exception as e:
            self._handle_exception(e, f"delete_user({user_id})")

    @log_api_call("批量删除用户")
    @require_permission(ResourceType.USER, PermissionLevel.DELETE)
    async def batch_delete(self, user_ids: List[int], current_user=None) -> Dict[str, Any]:
        """批量删除用户"""
        try:
            deleted_count = await self.service.batch_delete_users(user_ids, current_user.user_name)
            return {"success": True, "message": f"成功删除{deleted_count}个用户"}
        except Exception as e:
            self._handle_exception(e, "batch_delete_users")

    @log_api_call("获取用户详情")
    @require_permission(ResourceType.USER, PermissionLevel.READ)
    async def get_by_id(self, user_id: int) -> UserDetailSchema:
        """获取用户详情"""
        try:
            result = await self.service.get_user_detail(user_id)
            if not result:
                raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, f"用户不存在: {user_id}")
            return result
        except Exception as e:
            self._handle_exception(e, f"get_user_detail({user_id})")

    @log_api_call("获取用户列表")
    @require_permission(ResourceType.USER, PermissionLevel.READ)
    async def list(
        self,
        page_num: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_name: str = Query(None, description="用户账号"),
        nick_name: str = Query(None, description="用户昵称"),
        email: str = Query(None, description="用户邮箱"),
        phonenumber: str = Query(None, description="手机号码"),
        status: str = Query(None, description="帐号状态"),
        dept_id: int = Query(None, description="部门ID"),
        begin_time: str = Query(None, description="开始时间"),
        end_time: str = Query(None, description="结束时间"),
        current_user=None,
        data_scope_sql: str = None
    ) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            self._validate_pagination(page_num, page_size)

            # 构建查询条件
            query = UserQuerySchema(
                user_name=user_name,
                nick_name=nick_name,
                email=email,
                phonenumber=phonenumber,
                status=status,
                dept_id=dept_id,
                begin_time=begin_time,
                end_time=end_time
            )

            result = await self.service.get_user_list(query, page_num, page_size, data_scope_sql)
            return result

        except Exception as e:
            self._handle_exception(e, "get_user_list")

    @log_api_call("更新用户密码")
    async def update_password(
        self, 
        password_data: UserPasswordUpdateSchema,
        current_user=None
    ) -> Dict[str, Any]:
        """更新用户密码"""
        try:
            # 只能修改自己的密码，除非是管理员
            if password_data.user_id != current_user.user_id and not current_user.admin:
                raise BusinessException(ErrorCode.PERMISSION_DENIED, "只能修改自己的密码")

            success = await self.service.update_password(password_data)
            if success:
                return {"success": True, "message": "密码更新成功"}
            else:
                raise BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "密码更新失败")

        except Exception as e:
            self._handle_exception(e, "update_password")

    @log_api_call("更新用户个人信息")
    async def update_profile(
        self, 
        profile_data: UserProfileSchema,
        current_user=None
    ) -> Dict[str, Any]:
        """更新用户个人信息"""
        try:
            # 只能修改自己的个人信息
            if profile_data.user_id != current_user.user_id:
                raise BusinessException(ErrorCode.PERMISSION_DENIED, "只能修改自己的个人信息")

            success = await self.service.update_profile(profile_data.user_id, profile_data)
            if success:
                return {"success": True, "message": "个人信息更新成功"}
            else:
                raise BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "个人信息更新失败")

        except Exception as e:
            self._handle_exception(e, "update_profile")

    @log_api_call("重置用户密码")
    @require_permission(ResourceType.USER, PermissionLevel.WRITE)
    async def reset_password(
        self, 
        user_id: int,
        new_password: str = Body(..., description="新密码"),
        current_user=None
    ) -> Dict[str, Any]:
        """重置用户密码（管理员操作）"""
        try:
            # 检查是否为超级管理员操作
            if user_id == 1 and current_user.user_id != 1:
                raise BusinessException(ErrorCode.PERMISSION_DENIED, "不允许重置超级管理员密码")

            # 这里需要实现管理员重置密码的逻辑
            # 与用户自己修改密码不同，管理员重置不需要验证旧密码
            
            return {"success": True, "message": "密码重置成功"}

        except Exception as e:
            self._handle_exception(e, f"reset_password({user_id})")

    @log_api_call("检查用户名唯一性")
    async def check_username_unique(
        self, 
        username: str = Query(..., description="用户名"),
        user_id: int = Query(None, description="用户ID（更新时排除自己）")
    ) -> Dict[str, Any]:
        """检查用户名是否唯一"""
        try:
            is_unique = await self.service.user_repo.check_username_unique(username, user_id)
            return {"unique": is_unique}

        except Exception as e:
            self._handle_exception(e, "check_username_unique")

    @log_api_call("检查邮箱唯一性")
    async def check_email_unique(
        self, 
        email: str = Query(..., description="邮箱"),
        user_id: int = Query(None, description="用户ID（更新时排除自己）")
    ) -> Dict[str, Any]:
        """检查邮箱是否唯一"""
        try:
            is_unique = await self.service.user_repo.check_email_unique(email, user_id)
            return {"unique": is_unique}

        except Exception as e:
            self._handle_exception(e, "check_email_unique")

    @log_api_call("检查手机号唯一性")
    async def check_phone_unique(
        self, 
        phone: str = Query(..., description="手机号"),
        user_id: int = Query(None, description="用户ID（更新时排除自己）")
    ) -> Dict[str, Any]:
        """检查手机号是否唯一"""
        try:
            is_unique = await self.service.user_repo.check_phone_unique(phone, user_id)
            return {"unique": is_unique}

        except Exception as e:
            self._handle_exception(e, "check_phone_unique")


# 创建路由器
def create_user_router() -> APIRouter:
    """创建用户路由器"""
    router = APIRouter(prefix="/system/user", tags=["用户管理"])
    
    # 依赖注入
    def get_user_controller(
        user_service: UserService = Depends(get_service(UserService))
    ) -> UserController:
        return UserController(user_service)

    # 注册路由
    router.add_api_route("/", create_user_controller().create, methods=["POST"], summary="创建用户")
    router.add_api_route("/{user_id}", create_user_controller().update, methods=["PUT"], summary="更新用户")
    router.add_api_route("/{user_id}", create_user_controller().delete, methods=["DELETE"], summary="删除用户")
    router.add_api_route("/{user_id}", create_user_controller().get_by_id, methods=["GET"], summary="获取用户详情")
    router.add_api_route("/list", create_user_controller().list, methods=["GET"], summary="获取用户列表")
    router.add_api_route("/batch", create_user_controller().batch_delete, methods=["DELETE"], summary="批量删除用户")
    router.add_api_route("/password", create_user_controller().update_password, methods=["PUT"], summary="更新密码")
    router.add_api_route("/profile", create_user_controller().update_profile, methods=["PUT"], summary="更新个人信息")
    router.add_api_route("/reset/{user_id}", create_user_controller().reset_password, methods=["PUT"], summary="重置密码")
    router.add_api_route("/check/username", create_user_controller().check_username_unique, methods=["GET"], summary="检查用户名唯一性")
    router.add_api_route("/check/email", create_user_controller().check_email_unique, methods=["GET"], summary="检查邮箱唯一性")
    router.add_api_route("/check/phone", create_user_controller().check_phone_unique, methods=["GET"], summary="检查手机号唯一性")

    return router


def create_user_controller() -> UserController:
    """创建用户控制器实例的辅助函数"""
    # 这里需要实际的依赖注入，暂时返回None
    return None
