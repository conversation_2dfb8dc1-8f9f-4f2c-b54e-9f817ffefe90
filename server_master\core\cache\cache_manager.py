"""
缓存管理器模块
"""
import json
import hashlib
import asyncio
from typing import Any, Optional, Union, Callable, Dict
from datetime import timedelta
from functools import wraps
import aioredis

from ..logging.structured_logger import structured_logger
from ..exceptions import CacheException


class CacheManager:
    """缓存管理器"""

    _instance: Optional['CacheManager'] = None
    _redis: Optional[aioredis.Redis] = None

    def __init__(self, redis: aioredis.Redis):
        self.redis = redis
        self.default_ttl = 3600  # 默认1小时过期
        self.key_prefix = "mxtt:"

    @classmethod
    async def create_instance(cls, redis_url: str, **kwargs) -> 'CacheManager':
        """创建缓存管理器实例"""
        try:
            redis = aioredis.from_url(redis_url, **kwargs)
            await redis.ping()  # 测试连接
            
            instance = cls(redis)
            cls._instance = instance
            cls._redis = redis
            
            structured_logger.info("缓存管理器初始化成功")
            return instance
        except Exception as e:
            structured_logger.error(f"缓存管理器初始化失败: {e}")
            raise CacheException("缓存管理器初始化失败", cause=e)

    @classmethod
    def get_instance(cls) -> 'CacheManager':
        """获取缓存管理器实例"""
        if not cls._instance:
            raise CacheException("缓存管理器未初始化")
        return cls._instance

    @classmethod
    async def close(cls):
        """关闭缓存连接"""
        if cls._redis:
            await cls._redis.close()
            cls._redis = None
            cls._instance = None
            structured_logger.info("缓存连接已关闭")

    def _generate_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.key_prefix}{key}"

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            cache_key = self._generate_key(key)
            value = await self.redis.get(cache_key)
            
            if value:
                structured_logger.debug(f"缓存命中: {key}")
                return json.loads(value)
            else:
                structured_logger.debug(f"缓存未命中: {key}")
                return None
        except Exception as e:
            structured_logger.error(f"缓存获取失败: {key}, 错误: {e}")
            raise CacheException(f"缓存获取失败: {key}", operation="get", key=key, cause=e)

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            cache_key = self._generate_key(key)
            ttl = ttl or self.default_ttl
            serialized_value = json.dumps(value, default=str, ensure_ascii=False)
            
            await self.redis.setex(cache_key, ttl, serialized_value)
            structured_logger.debug(f"缓存设置成功: {key}, TTL: {ttl}")
            return True
        except Exception as e:
            structured_logger.error(f"缓存设置失败: {key}, 错误: {e}")
            raise CacheException(f"缓存设置失败: {key}", operation="set", key=key, cause=e)

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            cache_key = self._generate_key(key)
            result = await self.redis.delete(cache_key)
            structured_logger.debug(f"缓存删除: {key}, 结果: {result}")
            return result > 0
        except Exception as e:
            structured_logger.error(f"缓存删除失败: {key}, 错误: {e}")
            raise CacheException(f"缓存删除失败: {key}", operation="delete", key=key, cause=e)

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            cache_key = self._generate_key(key)
            result = await self.redis.exists(cache_key)
            return result > 0
        except Exception as e:
            structured_logger.error(f"缓存检查失败: {key}, 错误: {e}")
            return False

    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            cache_key = self._generate_key(key)
            result = await self.redis.expire(cache_key, ttl)
            return result
        except Exception as e:
            structured_logger.error(f"设置缓存过期时间失败: {key}, 错误: {e}")
            return False

    async def get_or_set(self, key: str, func: Callable, ttl: Optional[int] = None) -> Any:
        """获取缓存或设置缓存"""
        value = await self.get(key)
        if value is None:
            if asyncio.iscoroutinefunction(func):
                value = await func()
            else:
                value = func()
            await self.set(key, value, ttl)
        return value

    async def invalidate_pattern(self, pattern: str) -> int:
        """根据模式删除缓存"""
        try:
            cache_pattern = self._generate_key(pattern)
            keys = await self.redis.keys(cache_pattern)
            if keys:
                deleted_count = await self.redis.delete(*keys)
                structured_logger.info(f"批量删除缓存: {pattern}, 删除数量: {deleted_count}")
                return deleted_count
            return 0
        except Exception as e:
            structured_logger.error(f"批量删除缓存失败: {pattern}, 错误: {e}")
            raise CacheException(f"批量删除缓存失败: {pattern}", operation="invalidate_pattern", key=pattern, cause=e)

    async def increment(self, key: str, amount: int = 1) -> int:
        """递增计数器"""
        try:
            cache_key = self._generate_key(key)
            result = await self.redis.incrby(cache_key, amount)
            return result
        except Exception as e:
            structured_logger.error(f"递增计数器失败: {key}, 错误: {e}")
            raise CacheException(f"递增计数器失败: {key}", operation="increment", key=key, cause=e)

    async def decrement(self, key: str, amount: int = 1) -> int:
        """递减计数器"""
        try:
            cache_key = self._generate_key(key)
            result = await self.redis.decrby(cache_key, amount)
            return result
        except Exception as e:
            structured_logger.error(f"递减计数器失败: {key}, 错误: {e}")
            raise CacheException(f"递减计数器失败: {key}", operation="decrement", key=key, cause=e)

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            structured_logger.error(f"缓存健康检查失败: {e}")
            return False


# 缓存装饰器
def cache_result(key_prefix: str, ttl: int = 3600, key_generator: Optional[Callable] = None):
    """缓存结果装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_generator:
                cache_key = f"{key_prefix}:{key_generator(*args, **kwargs)}"
            else:
                # 使用参数哈希生成键
                params_str = str(args) + str(sorted(kwargs.items()))
                params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
                cache_key = f"{key_prefix}:{params_hash}"

            # 尝试从缓存获取
            cache_manager = CacheManager.get_instance()
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                structured_logger.debug(f"缓存命中: {cache_key}")
                return cached_result

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            structured_logger.debug(f"缓存设置: {cache_key}")
            return result
        return wrapper
    return decorator


def cache_invalidate(pattern: str):
    """缓存失效装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            # 执行成功后清除相关缓存
            cache_manager = CacheManager.get_instance()
            await cache_manager.invalidate_pattern(pattern)
            structured_logger.debug(f"缓存失效: {pattern}")
            
            return result
        return wrapper
    return decorator


# 分层缓存服务
class LayeredCacheService:
    """分层缓存服务"""

    def __init__(self, l1_cache: Dict[str, Any], l2_cache: CacheManager):
        self.l1_cache = l1_cache  # 内存缓存
        self.l2_cache = l2_cache  # Redis缓存
        self.l1_max_size = 1000

    async def get(self, key: str) -> Optional[Any]:
        """分层获取缓存"""
        # 先从L1缓存获取
        if key in self.l1_cache:
            structured_logger.debug(f"L1缓存命中: {key}")
            return self.l1_cache[key]

        # 从L2缓存获取
        value = await self.l2_cache.get(key)
        if value is not None:
            # 写入L1缓存
            self._set_l1_cache(key, value)
            structured_logger.debug(f"L2缓存命中，写入L1: {key}")

        return value

    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """分层设置缓存"""
        # 设置L1缓存
        self._set_l1_cache(key, value)
        # 设置L2缓存
        await self.l2_cache.set(key, value, ttl)

    async def delete(self, key: str):
        """分层删除缓存"""
        # 删除L1缓存
        if key in self.l1_cache:
            del self.l1_cache[key]
        # 删除L2缓存
        await self.l2_cache.delete(key)

    def _set_l1_cache(self, key: str, value: Any):
        """设置L1缓存"""
        if len(self.l1_cache) >= self.l1_max_size:
            # LRU淘汰策略
            oldest_key = next(iter(self.l1_cache))
            del self.l1_cache[oldest_key]
        self.l1_cache[key] = value
