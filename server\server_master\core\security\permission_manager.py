"""
权限管理器模块
"""
from enum import Enum
from typing import List, Dict, Any, Optional, Set
from functools import wraps
from fastapi import Depends, HTTPException, status, Request

from ..logging.structured_logger import structured_logger
from ..exceptions import AuthorizationException, ErrorCode
from ..cache.cache_manager import CacheManager


class PermissionLevel(Enum):
    """权限级别"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"


class ResourceType(Enum):
    """资源类型"""
    USER = "user"
    ROLE = "role"
    MENU = "menu"
    DEPT = "dept"
    POST = "post"
    DICT = "dict"
    CONFIG = "config"
    GENERATOR = "generator"
    SCHEDULER = "scheduler"


class DataScope(Enum):
    """数据权限范围"""
    ALL = "all"  # 全部数据权限
    CUSTOM = "custom"  # 自定数据权限
    DEPT = "dept"  # 部门数据权限
    DEPT_AND_CHILD = "dept_and_child"  # 部门及以下数据权限
    SELF = "self"  # 仅本人数据权限


class PermissionManager:
    """权限管理器"""

    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager
        self.permission_cache_ttl = 1800  # 30分钟缓存

    async def check_permission(
        self,
        user_id: int,
        resource: ResourceType,
        action: PermissionLevel,
        resource_id: Optional[int] = None
    ) -> bool:
        """检查用户权限"""
        try:
            # 获取用户权限
            user_permissions = await self._get_user_permissions(user_id)

            # 检查超级管理员权限
            if "*:*:*" in user_permissions:
                structured_logger.debug(f"超级管理员权限通过: {user_id}")
                return True

            # 检查资源权限
            permission_key = f"{resource.value}:{action.value}"
            if permission_key in user_permissions:
                structured_logger.debug(f"资源权限通过: {user_id}, {permission_key}")
                return True

            # 检查具体资源权限
            if resource_id:
                specific_permission = f"{resource.value}:{action.value}:{resource_id}"
                if specific_permission in user_permissions:
                    structured_logger.debug(f"具体资源权限通过: {user_id}, {specific_permission}")
                    return True

            structured_logger.warning(
                f"权限检查失败: {user_id}, {resource.value}:{action.value}"
            )
            return False

        except Exception as e:
            structured_logger.error(f"权限检查异常: {e}")
            return False

    async def check_data_scope(
        self,
        user_id: int,
        resource: ResourceType,
        data_scope: DataScope,
        dept_id: Optional[int] = None
    ) -> bool:
        """检查数据权限范围"""
        try:
            user_data_scope = await self._get_user_data_scope(user_id, resource)
            
            if user_data_scope == DataScope.ALL:
                return True
            elif user_data_scope == DataScope.CUSTOM:
                # 需要检查自定义数据权限
                return await self._check_custom_data_permission(user_id, resource, dept_id)
            elif user_data_scope == DataScope.DEPT:
                # 检查部门权限
                user_dept_id = await self._get_user_dept_id(user_id)
                return dept_id == user_dept_id
            elif user_data_scope == DataScope.DEPT_AND_CHILD:
                # 检查部门及子部门权限
                user_dept_ids = await self._get_user_dept_and_child_ids(user_id)
                return dept_id in user_dept_ids
            elif user_data_scope == DataScope.SELF:
                # 只能访问自己的数据
                return await self._check_self_data_permission(user_id, resource, dept_id)
            
            return False

        except Exception as e:
            structured_logger.error(f"数据权限检查异常: {e}")
            return False

    async def get_data_scope_sql(
        self,
        user_id: int,
        resource: ResourceType,
        table_alias: str = ""
    ) -> str:
        """获取数据权限SQL条件"""
        try:
            user_data_scope = await self._get_user_data_scope(user_id, resource)
            table_prefix = f"{table_alias}." if table_alias else ""

            if user_data_scope == DataScope.ALL:
                return "1=1"  # 无限制
            elif user_data_scope == DataScope.CUSTOM:
                dept_ids = await self._get_user_custom_dept_ids(user_id)
                if dept_ids:
                    dept_ids_str = ",".join(map(str, dept_ids))
                    return f"{table_prefix}dept_id IN ({dept_ids_str})"
                else:
                    return "1=0"  # 无权限
            elif user_data_scope == DataScope.DEPT:
                user_dept_id = await self._get_user_dept_id(user_id)
                return f"{table_prefix}dept_id = {user_dept_id}"
            elif user_data_scope == DataScope.DEPT_AND_CHILD:
                dept_ids = await self._get_user_dept_and_child_ids(user_id)
                dept_ids_str = ",".join(map(str, dept_ids))
                return f"{table_prefix}dept_id IN ({dept_ids_str})"
            elif user_data_scope == DataScope.SELF:
                return f"{table_prefix}create_by = {user_id}"
            
            return "1=0"  # 默认无权限

        except Exception as e:
            structured_logger.error(f"获取数据权限SQL失败: {e}")
            return "1=0"

    async def _get_user_permissions(self, user_id: int) -> Set[str]:
        """获取用户权限列表"""
        cache_key = f"user_permissions:{user_id}"
        
        if self.cache_manager:
            cached_permissions = await self.cache_manager.get(cache_key)
            if cached_permissions:
                return set(cached_permissions)

        # 从数据库查询权限
        permissions = await self._query_user_permissions(user_id)
        
        if self.cache_manager:
            await self.cache_manager.set(cache_key, list(permissions), self.permission_cache_ttl)

        return permissions

    async def _query_user_permissions(self, user_id: int) -> Set[str]:
        """从数据库查询用户权限"""
        # 这里需要根据实际的数据库结构实现
        # 示例实现
        permissions = set()
        
        # 查询用户角色权限
        # SELECT p.perms FROM sys_user_role ur 
        # LEFT JOIN sys_role_menu rm ON ur.role_id = rm.role_id 
        # LEFT JOIN sys_menu p ON rm.menu_id = p.menu_id 
        # WHERE ur.user_id = ? AND p.perms IS NOT NULL
        
        # 这里返回示例权限
        permissions.add("user:read")
        permissions.add("user:write")
        
        return permissions

    async def _get_user_data_scope(self, user_id: int, resource: ResourceType) -> DataScope:
        """获取用户数据权限范围"""
        cache_key = f"user_data_scope:{user_id}:{resource.value}"
        
        if self.cache_manager:
            cached_scope = await self.cache_manager.get(cache_key)
            if cached_scope:
                return DataScope(cached_scope)

        # 从数据库查询数据权限范围
        data_scope = await self._query_user_data_scope(user_id, resource)
        
        if self.cache_manager:
            await self.cache_manager.set(cache_key, data_scope.value, self.permission_cache_ttl)

        return data_scope

    async def _query_user_data_scope(self, user_id: int, resource: ResourceType) -> DataScope:
        """从数据库查询用户数据权限范围"""
        # 这里需要根据实际的数据库结构实现
        # 示例实现
        return DataScope.DEPT

    async def _get_user_dept_id(self, user_id: int) -> int:
        """获取用户部门ID"""
        # 实现获取用户部门ID的逻辑
        return 1

    async def _get_user_dept_and_child_ids(self, user_id: int) -> List[int]:
        """获取用户部门及子部门ID列表"""
        # 实现获取用户部门及子部门ID的逻辑
        return [1, 2, 3]

    async def _get_user_custom_dept_ids(self, user_id: int) -> List[int]:
        """获取用户自定义数据权限部门ID列表"""
        # 实现获取用户自定义数据权限的逻辑
        return [1, 2]

    async def _check_custom_data_permission(self, user_id: int, resource: ResourceType, dept_id: Optional[int]) -> bool:
        """检查自定义数据权限"""
        custom_dept_ids = await self._get_user_custom_dept_ids(user_id)
        return dept_id in custom_dept_ids

    async def _check_self_data_permission(self, user_id: int, resource: ResourceType, resource_owner_id: Optional[int]) -> bool:
        """检查自身数据权限"""
        return user_id == resource_owner_id

    async def invalidate_user_permissions(self, user_id: int):
        """清除用户权限缓存"""
        if self.cache_manager:
            await self.cache_manager.invalidate_pattern(f"user_permissions:{user_id}")
            await self.cache_manager.invalidate_pattern(f"user_data_scope:{user_id}:*")
            structured_logger.info(f"用户权限缓存已清除: {user_id}")


# 权限装饰器
def require_permission(resource: ResourceType, action: PermissionLevel):
    """权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取当前用户
            current_user = None
            for arg in args:
                if hasattr(arg, 'user_id'):
                    current_user = arg
                    break
            
            if not current_user:
                # 尝试从kwargs获取
                current_user = kwargs.get('current_user')
            
            if not current_user:
                raise AuthorizationException("未找到当前用户信息")

            # 检查权限
            permission_manager = PermissionManager()
            has_permission = await permission_manager.check_permission(
                current_user.user_id,
                resource,
                action
            )

            if not has_permission:
                raise AuthorizationException(
                    f"权限不足: 需要{resource.value}:{action.value}权限",
                    required_permission=f"{resource.value}:{action.value}"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_data_scope(resource: ResourceType, data_scope: DataScope):
    """数据权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户和部门ID
            current_user = kwargs.get('current_user')
            dept_id = kwargs.get('dept_id')
            
            if not current_user:
                raise AuthorizationException("未找到当前用户信息")

            # 检查数据权限
            permission_manager = PermissionManager()
            has_permission = await permission_manager.check_data_scope(
                current_user.user_id,
                resource,
                data_scope,
                dept_id
            )

            if not has_permission:
                raise AuthorizationException(
                    f"数据权限不足: 需要{resource.value}:{data_scope.value}权限"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator
