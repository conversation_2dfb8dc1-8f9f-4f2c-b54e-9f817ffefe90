# -*- coding: utf-8 -*-
"""
@author: peng
@file: trace_middleware.py
@time: 2025/1/17  16:57
"""

import asyncio
import contextvars
from typing import Optional
from uuid import uuid4

from fastapi import Fast<PERSON>I
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response

# Context variables
CTX_REQUEST_ID: contextvars.ContextVar[str] = contextvars.ContextVar('request-id', default='')
_span_context: contextvars.ContextVar[Optional['Span']] = contextvars.ContextVar('span', default=None)


class TraceCtx:
    @staticmethod
    def set_id():
        _id = uuid4().hex
        CTX_REQUEST_ID.set(_id)
        return _id

    @staticmethod
    def get_id():
        return CTX_REQUEST_ID.get()


class Span:
    def __init__(self, name: str):
        self.name = name
        self.parent: Optional[Span] = None
        self._token: Optional[contextvars.Token] = None

    async def __aenter__(self):
        parent_span = _span_context.get()
        self.parent = parent_span
        self._token = _span_context.set(self)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._token is not None:
            _span_context.reset(self._token)


async def get_current_span() -> Optional[Span]:
    return _span_context.get()


class TraceASGIMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        trace_id = request.headers.get('X-Request-ID')
        if not trace_id:
            trace_id = TraceCtx.set_id()

        async with Span(name=f"{request.method} {request.url.path}"):
            response = await call_next(request)
            response.headers['X-Request-ID'] = trace_id
            return response


def add_trace_middleware(app: FastAPI):
    """
    添加trace中间件

    :param app: FastAPI对象
    :return:
    """
    app.add_middleware(TraceASGIMiddleware)


__all__ = ('TraceASGIMiddleware', 'TraceCtx', 'Span', 'get_current_span', 'add_trace_middleware')
__version__ = '0.1.0'
