"""
用户服务层模块
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from core.base.service import BusinessService
from core.dependencies import service
from core.security.password import PasswordSecurity
from core.exceptions import BusinessException, ValidationException, ErrorCode
from core.logging.structured_logger import structured_logger, log_execution_time
from core.cache.cache_manager import cache_result, cache_invalidate

from ..repositories.user import UserRepository, UserRoleRepository, UserPostRepository
from ..schemas.user import (
    UserCreateSchema, UserUpdateSchema, UserQuerySchema, UserResponseSchema,
    UserDetailSchema, UserPasswordUpdateSchema, UserProfileSchema
)


@service()
class UserService(BusinessService):
    """用户服务类"""

    def __init__(self, session: AsyncSession, user_repo: UserRepository, 
                 user_role_repo: UserRoleRepository, user_post_repo: UserPostRepository):
        super().__init__(session)
        self.user_repo = user_repo
        self.user_role_repo = user_role_repo
        self.user_post_repo = user_post_repo

    @log_execution_time("用户创建")
    async def create_user(self, user_data: UserCreateSchema, creator: str) -> UserResponseSchema:
        """创建用户"""
        async with await self.begin_transaction():
            try:
                # 验证用户名唯一性
                if not await self.user_repo.check_username_unique(user_data.user_name):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"用户名 {user_data.user_name} 已存在")

                # 验证邮箱唯一性
                if user_data.email and not await self.user_repo.check_email_unique(user_data.email):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"邮箱 {user_data.email} 已存在")

                # 验证手机号唯一性
                if user_data.phonenumber and not await self.user_repo.check_phone_unique(user_data.phonenumber):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"手机号 {user_data.phonenumber} 已存在")

                # 生成密码盐值和哈希
                salt = PasswordSecurity.generate_salt()
                password_hash = PasswordSecurity.hash_password(user_data.password, salt)

                # 创建用户数据
                user_dict = user_data.model_dump(exclude={'password', 'role_ids', 'post_ids'})
                user_dict.update({
                    'password': password_hash,
                    'salt': salt,
                    'create_by': creator
                })

                # 创建用户
                user = await self.user_repo.create(user_dict)

                # 关联角色
                if user_data.role_ids:
                    await self.user_role_repo.batch_insert_user_roles(user.user_id, user_data.role_ids)

                # 关联岗位
                if user_data.post_ids:
                    await self.user_post_repo.batch_insert_user_posts(user.user_id, user_data.post_ids)

                await self.commit_transaction()

                structured_logger.info(f"用户创建成功: {user.user_name}", user_id=user.user_id)
                return UserResponseSchema.model_validate(user)

            except Exception as e:
                await self.rollback_transaction()
                structured_logger.error(f"用户创建失败: {e}")
                raise

    @log_execution_time("用户更新")
    async def update_user(self, user_data: UserUpdateSchema, updater: str) -> UserResponseSchema:
        """更新用户"""
        async with await self.begin_transaction():
            try:
                # 检查用户是否存在
                existing_user = await self.user_repo.get_by_id(user_data.user_id)
                if not existing_user:
                    raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, f"用户不存在: {user_data.user_id}")

                # 检查是否为超级管理员
                if existing_user.user_id == 1 and updater != 'admin':
                    raise BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "不允许修改超级管理员")

                # 验证用户名唯一性（排除自己）
                if not await self.user_repo.check_username_unique(user_data.user_name, user_data.user_id):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"用户名 {user_data.user_name} 已存在")

                # 验证邮箱唯一性（排除自己）
                if user_data.email and not await self.user_repo.check_email_unique(user_data.email, user_data.user_id):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"邮箱 {user_data.email} 已存在")

                # 验证手机号唯一性（排除自己）
                if user_data.phonenumber and not await self.user_repo.check_phone_unique(user_data.phonenumber, user_data.user_id):
                    raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"手机号 {user_data.phonenumber} 已存在")

                # 更新用户数据
                update_dict = user_data.model_dump(exclude={'user_id', 'role_ids', 'post_ids'})
                update_dict['update_by'] = updater

                user = await self.user_repo.update(user_data.user_id, update_dict)

                # 更新角色关联
                await self.user_role_repo.delete_user_roles(user_data.user_id)
                if user_data.role_ids:
                    await self.user_role_repo.batch_insert_user_roles(user_data.user_id, user_data.role_ids)

                # 更新岗位关联
                await self.user_post_repo.delete_user_posts(user_data.user_id)
                if user_data.post_ids:
                    await self.user_post_repo.batch_insert_user_posts(user_data.user_id, user_data.post_ids)

                await self.commit_transaction()

                structured_logger.info(f"用户更新成功: {user.user_name}", user_id=user.user_id)
                return UserResponseSchema.model_validate(user)

            except Exception as e:
                await self.rollback_transaction()
                structured_logger.error(f"用户更新失败: {e}")
                raise

    @log_execution_time("用户删除")
    async def delete_user(self, user_id: int, deleter: str) -> bool:
        """删除用户（软删除）"""
        try:
            # 检查用户是否存在
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, f"用户不存在: {user_id}")

            # 检查是否为超级管理员
            if user.user_id == 1:
                raise BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "不允许删除超级管理员")

            # 软删除用户
            success = await self.user_repo.soft_delete(user_id)
            
            if success:
                structured_logger.info(f"用户删除成功: {user.user_name}", user_id=user_id)
            
            return success

        except Exception as e:
            structured_logger.error(f"用户删除失败: {e}")
            raise

    @log_execution_time("批量用户删除")
    async def batch_delete_users(self, user_ids: List[int], deleter: str) -> int:
        """批量删除用户"""
        try:
            # 过滤掉超级管理员
            filtered_ids = [uid for uid in user_ids if uid != 1]
            
            if not filtered_ids:
                raise BusinessException(ErrorCode.BAD_REQUEST, "没有可删除的用户")

            deleted_count = await self.user_repo.batch_soft_delete(filtered_ids)
            
            structured_logger.info(f"批量删除用户成功: {deleted_count}个", user_ids=filtered_ids)
            return deleted_count

        except Exception as e:
            structured_logger.error(f"批量删除用户失败: {e}")
            raise

    @cache_result("user_detail", ttl=300)
    async def get_user_detail(self, user_id: int) -> Optional[UserDetailSchema]:
        """获取用户详情"""
        try:
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                return None

            # 获取用户角色
            role_ids = await self.user_role_repo.get_user_roles(user_id)
            
            # 获取用户岗位
            post_ids = await self.user_post_repo.get_user_posts(user_id)

            # 构建详情数据
            user_detail = UserDetailSchema.model_validate(user)
            user_detail.roles = [{'role_id': rid} for rid in role_ids]  # 简化版本，实际需要查询角色详情
            user_detail.posts = [{'post_id': pid} for pid in post_ids]  # 简化版本，实际需要查询岗位详情

            return user_detail

        except Exception as e:
            structured_logger.error(f"获取用户详情失败: {e}")
            raise

    async def get_user_list(
        self, 
        query: UserQuerySchema, 
        page_num: int = 1, 
        page_size: int = 10,
        data_scope_sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            skip = (page_num - 1) * page_size
            
            # 获取用户列表
            users = await self.user_repo.list_with_dept_info(query, skip, page_size, data_scope_sql)
            
            # 获取总数
            total = await self.user_repo.count_with_filters(query, data_scope_sql)

            return {
                'rows': [UserResponseSchema.model_validate(user) for user in users],
                'total': total,
                'page_num': page_num,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }

        except Exception as e:
            structured_logger.error(f"获取用户列表失败: {e}")
            raise

    async def update_password(self, password_data: UserPasswordUpdateSchema) -> bool:
        """更新用户密码"""
        try:
            # 获取用户信息
            user = await self.user_repo.get_by_id(password_data.user_id)
            if not user:
                raise BusinessException(ErrorCode.RESOURCE_NOT_FOUND, "用户不存在")

            # 验证旧密码
            if not PasswordSecurity.verify_password(password_data.old_password, user.salt, user.password):
                raise BusinessException(ErrorCode.VALIDATION_ERROR, "旧密码不正确")

            # 生成新密码哈希
            new_salt = PasswordSecurity.generate_salt()
            new_password_hash = PasswordSecurity.hash_password(password_data.new_password, new_salt)

            # 更新密码
            success = await self.user_repo.update(password_data.user_id, {
                'password': new_password_hash,
                'salt': new_salt
            })

            if success:
                structured_logger.info(f"用户密码更新成功", user_id=password_data.user_id)

            return bool(success)

        except Exception as e:
            structured_logger.error(f"更新用户密码失败: {e}")
            raise

    async def update_profile(self, user_id: int, profile_data: UserProfileSchema) -> bool:
        """更新用户个人信息"""
        try:
            # 验证邮箱唯一性
            if profile_data.email and not await self.user_repo.check_email_unique(profile_data.email, user_id):
                raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"邮箱 {profile_data.email} 已存在")

            # 验证手机号唯一性
            if profile_data.phonenumber and not await self.user_repo.check_phone_unique(profile_data.phonenumber, user_id):
                raise BusinessException(ErrorCode.RESOURCE_ALREADY_EXISTS, f"手机号 {profile_data.phonenumber} 已存在")

            # 更新个人信息
            update_dict = profile_data.model_dump(exclude={'user_id'})
            user = await self.user_repo.update(user_id, update_dict)

            if user:
                structured_logger.info(f"用户个人信息更新成功", user_id=user_id)

            return bool(user)

        except Exception as e:
            structured_logger.error(f"更新用户个人信息失败: {e}")
            raise

    async def get_by_username(self, username: str) -> Optional[UserResponseSchema]:
        """根据用户名获取用户"""
        try:
            user = await self.user_repo.get_by_username(username)
            if user:
                return UserResponseSchema.model_validate(user)
            return None

        except Exception as e:
            structured_logger.error(f"根据用户名获取用户失败: {e}")
            raise

    async def update_login_info(self, user_id: int, login_ip: str) -> bool:
        """更新登录信息"""
        try:
            success = await self.user_repo.update_login_info(user_id, login_ip)
            if success:
                structured_logger.info(f"用户登录信息更新成功", user_id=user_id, login_ip=login_ip)
            return success

        except Exception as e:
            structured_logger.error(f"更新用户登录信息失败: {e}")
            raise
