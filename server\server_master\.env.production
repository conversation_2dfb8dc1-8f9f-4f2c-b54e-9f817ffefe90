# 生产环境配置

# 应用配置
APP_NAME=MXTT-FastAPI
APP_VERSION=2.0.0
APP_ENVIRONMENT=production
APP_DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=9099
APP_SECRET_KEY=your-production-secret-key-must-be-changed
APP_WORKERS=4

# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=app_user
DB_PASSWORD=secure_password
DB_DATABASE=mxtt_production
DB_ECHO=false
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=1800
DB_POOL_TIMEOUT=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=100

# JWT配置
APP_JWT_ALGORITHM=HS256
APP_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
APP_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 日志配置
APP_LOG_LEVEL=INFO
APP_LOG_FORMAT=json

# 安全配置
APP_ALLOWED_HOSTS=["yourdomain.com", "www.yourdomain.com"]
APP_CORS_ORIGINS=["https://yourdomain.com"]
APP_ENABLE_CORS=true

# 限流配置
APP_RATE_LIMIT_ENABLED=true
APP_DEFAULT_RATE_LIMIT=100
APP_DEFAULT_RATE_WINDOW=60

# 监控配置
APP_ENABLE_METRICS=true
APP_ENABLE_PERFORMANCE_MONITORING=true
APP_METRICS_PORT=9090

# 调度器配置
APP_SCHEDULER_ENABLED=true
APP_SCHEDULER_TIMEZONE=Asia/Shanghai

# 文件上传配置
APP_UPLOAD_MAX_SIZE=10485760
APP_UPLOAD_PATH=/var/app/storage/uploads

# SSL配置
DB_SSL_DISABLED=false
REDIS_SSL_ENABLED=true
