"""
数据库管理器模块
"""
import asyncio
from typing import Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy import event, text
from sqlalchemy.engine import Engine

from ..logging.structured_logger import structured_logger
from ..exceptions import DatabaseException, ErrorCode


class DatabaseManager:
    """数据库管理器"""

    _engine: Optional[Engine] = None
    _async_session_factory: Optional[async_sessionmaker] = None

    @classmethod
    def init_database(cls, database_url: str, **kwargs):
        """初始化数据库"""
        try:
            # 默认配置
            default_config = {
                'echo': False,  # 生产环境关闭SQL日志
                'pool_size': 20,  # 根据并发量调整
                'max_overflow': 30,  # 增加溢出连接
                'pool_recycle': 1800,  # 缩短连接回收时间
                'pool_timeout': 10,  # 缩短超时时间
                'pool_pre_ping': True,  # 启用连接预检
                'poolclass': QueuePool,
                'connect_args': {
                    'charset': 'utf8mb4',
                    'autocommit': False,
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                }
            }

            # 合并用户配置
            config = {**default_config, **kwargs}

            # 创建异步引擎
            cls._engine = create_async_engine(database_url, **config)

            # 创建会话工厂
            cls._async_session_factory = async_sessionmaker(
                cls._engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # 设置事件监听器
            cls._setup_event_listeners()

            structured_logger.info(
                "数据库初始化成功",
                pool_size=config['pool_size'],
                max_overflow=config['max_overflow'],
                pool_recycle=config['pool_recycle']
            )

        except Exception as e:
            structured_logger.error(f"数据库初始化失败: {e}")
            raise DatabaseException("数据库初始化失败", cause=e)

    @classmethod
    def _setup_event_listeners(cls):
        """设置事件监听器"""
        if not cls._engine:
            return

        @event.listens_for(cls._engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """设置数据库连接参数"""
            if 'sqlite' in str(cls._engine.url):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()

        @event.listens_for(cls._engine.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出事件"""
            structured_logger.debug("数据库连接检出")

        @event.listens_for(cls._engine.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入事件"""
            structured_logger.debug("数据库连接检入")

    @classmethod
    def get_async_session_factory(cls) -> async_sessionmaker:
        """获取异步会话工厂"""
        if not cls._async_session_factory:
            raise DatabaseException("数据库未初始化")
        return cls._async_session_factory

    @classmethod
    def get_engine(cls):
        """获取数据库引擎"""
        if not cls._engine:
            raise DatabaseException("数据库未初始化")
        return cls._engine

    @classmethod
    async def get_session(cls) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if not cls._async_session_factory:
            raise DatabaseException("数据库未初始化")

        async with cls._async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                structured_logger.error(f"数据库会话异常: {e}")
                raise DatabaseException("数据库操作失败", cause=e)
            finally:
                await session.close()

    @classmethod
    async def execute_raw_sql(cls, sql: str, params: dict = None) -> any:
        """执行原生SQL"""
        async with cls.get_session() as session:
            try:
                result = await session.execute(text(sql), params or {})
                return result
            except Exception as e:
                structured_logger.error(f"执行SQL失败: {sql}, 错误: {e}")
                raise DatabaseException(f"SQL执行失败: {sql}", cause=e)

    @classmethod
    async def health_check(cls) -> bool:
        """健康检查"""
        try:
            async with cls.get_session() as session:
                await session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            structured_logger.error(f"数据库健康检查失败: {e}")
            return False

    @classmethod
    async def get_connection_info(cls) -> dict:
        """获取连接信息"""
        if not cls._engine:
            return {}

        pool = cls._engine.pool
        return {
            'pool_size': pool.size(),
            'checked_in': pool.checkedin(),
            'checked_out': pool.checkedout(),
            'overflow': pool.overflow(),
            'invalid': pool.invalid()
        }

    @classmethod
    async def close(cls):
        """关闭数据库连接"""
        if cls._engine:
            await cls._engine.dispose()
            cls._engine = None
            cls._async_session_factory = None
            structured_logger.info("数据库连接已关闭")


class TransactionManager:
    """事务管理器"""

    def __init__(self, session: AsyncSession):
        self.session = session
        self._transaction = None

    async def __aenter__(self):
        """进入事务上下文"""
        self._transaction = await self.session.begin()
        structured_logger.debug("事务开始")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出事务上下文"""
        if exc_type:
            await self._transaction.rollback()
            structured_logger.warning(f"事务回滚: {exc_val}")
        else:
            await self._transaction.commit()
            structured_logger.debug("事务提交")

    async def commit(self):
        """提交事务"""
        if self._transaction:
            await self._transaction.commit()
            structured_logger.debug("手动提交事务")

    async def rollback(self):
        """回滚事务"""
        if self._transaction:
            await self._transaction.rollback()
            structured_logger.debug("手动回滚事务")


class DatabaseMetrics:
    """数据库指标收集器"""

    def __init__(self):
        self.query_count = 0
        self.slow_query_count = 0
        self.error_count = 0
        self.total_query_time = 0.0

    def record_query(self, execution_time: float, is_error: bool = False, is_slow: bool = False):
        """记录查询指标"""
        self.query_count += 1
        self.total_query_time += execution_time

        if is_error:
            self.error_count += 1

        if is_slow:
            self.slow_query_count += 1

    def get_metrics(self) -> dict:
        """获取指标"""
        avg_query_time = self.total_query_time / self.query_count if self.query_count > 0 else 0

        return {
            'query_count': self.query_count,
            'slow_query_count': self.slow_query_count,
            'error_count': self.error_count,
            'avg_query_time': avg_query_time,
            'error_rate': self.error_count / self.query_count if self.query_count > 0 else 0
        }

    def reset(self):
        """重置指标"""
        self.query_count = 0
        self.slow_query_count = 0
        self.error_count = 0
        self.total_query_time = 0.0


# 全局数据库指标实例
db_metrics = DatabaseMetrics()
