"""
任务调度器服务
"""
import asyncio
import importlib
import traceback
from datetime import datetime
from typing import List, Dict, Any, Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import Async<PERSON>Executor
from sqlalchemy.ext.asyncio import AsyncSession

from core.base.service import BusinessService
from core.dependencies import service
from core.exceptions import BusinessException, ErrorCode
from core.logging.structured_logger import structured_logger, log_execution_time

from ..models.job import SysJobModel, SysJobLogModel


@service()
class SchedulerService(BusinessService):
    """任务调度器服务"""

    def __init__(self, session: AsyncSession):
        super().__init__(session)
        self.scheduler = None
        self._init_scheduler()

    def _init_scheduler(self):
        """初始化调度器"""
        try:
            jobstores = {
                'default': MemoryJobStore()
            }
            
            executors = {
                'default': AsyncIOExecutor()
            }
            
            job_defaults = {
                'coalesce': False,
                'max_instances': 3
            }
            
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )
            
            structured_logger.info("任务调度器初始化成功")
            
        except Exception as e:
            structured_logger.error(f"任务调度器初始化失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "任务调度器初始化失败")

    async def start_scheduler(self):
        """启动调度器"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                structured_logger.info("任务调度器启动成功")
                
                # 加载数据库中的任务
                await self._load_jobs_from_database()
                
        except Exception as e:
            structured_logger.error(f"任务调度器启动失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "任务调度器启动失败")

    async def shutdown_scheduler(self):
        """关闭调度器"""
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown()
                structured_logger.info("任务调度器关闭成功")
                
        except Exception as e:
            structured_logger.error(f"任务调度器关闭失败: {e}")

    @log_execution_time("添加定时任务")
    async def add_job(self, job_data: Dict[str, Any]) -> bool:
        """添加定时任务"""
        try:
            job_id = str(job_data['job_id'])
            job_name = job_data['job_name']
            job_group = job_data['job_group']
            invoke_target = job_data['invoke_target']
            cron_expression = job_data['cron_expression']
            
            # 解析调用目标
            func = await self._parse_invoke_target(invoke_target)
            
            # 创建Cron触发器
            trigger = CronTrigger.from_crontab(cron_expression)
            
            # 添加任务到调度器
            self.scheduler.add_job(
                func=self._execute_job,
                trigger=trigger,
                id=job_id,
                name=job_name,
                args=[job_data],
                replace_existing=True
            )
            
            structured_logger.info(f"定时任务添加成功: {job_name}")
            return True
            
        except Exception as e:
            structured_logger.error(f"添加定时任务失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, f"添加定时任务失败: {e}")

    @log_execution_time("删除定时任务")
    async def remove_job(self, job_id: str) -> bool:
        """删除定时任务"""
        try:
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                structured_logger.info(f"定时任务删除成功: {job_id}")
                return True
            else:
                structured_logger.warning(f"定时任务不存在: {job_id}")
                return False
                
        except Exception as e:
            structured_logger.error(f"删除定时任务失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, f"删除定时任务失败: {e}")

    @log_execution_time("暂停定时任务")
    async def pause_job(self, job_id: str) -> bool:
        """暂停定时任务"""
        try:
            if self.scheduler.get_job(job_id):
                self.scheduler.pause_job(job_id)
                structured_logger.info(f"定时任务暂停成功: {job_id}")
                return True
            else:
                structured_logger.warning(f"定时任务不存在: {job_id}")
                return False
                
        except Exception as e:
            structured_logger.error(f"暂停定时任务失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, f"暂停定时任务失败: {e}")

    @log_execution_time("恢复定时任务")
    async def resume_job(self, job_id: str) -> bool:
        """恢复定时任务"""
        try:
            if self.scheduler.get_job(job_id):
                self.scheduler.resume_job(job_id)
                structured_logger.info(f"定时任务恢复成功: {job_id}")
                return True
            else:
                structured_logger.warning(f"定时任务不存在: {job_id}")
                return False
                
        except Exception as e:
            structured_logger.error(f"恢复定时任务失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, f"恢复定时任务失败: {e}")

    @log_execution_time("立即执行任务")
    async def run_job_once(self, job_id: str) -> bool:
        """立即执行任务"""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                # 获取任务数据
                job_data = job.args[0] if job.args else {}
                await self._execute_job(job_data)
                structured_logger.info(f"任务立即执行成功: {job_id}")
                return True
            else:
                structured_logger.warning(f"定时任务不存在: {job_id}")
                return False
                
        except Exception as e:
            structured_logger.error(f"立即执行任务失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, f"立即执行任务失败: {e}")

    async def get_job_list(self) -> List[Dict[str, Any]]:
        """获取任务列表"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                job_info = {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'pending': job.pending
                }
                jobs.append(job_info)
            
            return jobs
            
        except Exception as e:
            structured_logger.error(f"获取任务列表失败: {e}")
            raise BusinessException(ErrorCode.SYSTEM_ERROR, "获取任务列表失败")

    async def _load_jobs_from_database(self):
        """从数据库加载任务"""
        try:
            # 查询启用的任务
            from sqlalchemy import select
            stmt = select(SysJobModel).where(SysJobModel.status == '0')
            result = await self.session.execute(stmt)
            jobs = result.scalars().all()
            
            for job in jobs:
                job_data = {
                    'job_id': job.job_id,
                    'job_name': job.job_name,
                    'job_group': job.job_group,
                    'invoke_target': job.invoke_target,
                    'cron_expression': job.cron_expression,
                    'concurrent': job.concurrent
                }
                
                try:
                    await self.add_job(job_data)
                except Exception as e:
                    structured_logger.error(f"加载任务失败: {job.job_name}, 错误: {e}")
            
            structured_logger.info(f"从数据库加载了 {len(jobs)} 个任务")
            
        except Exception as e:
            structured_logger.error(f"从数据库加载任务失败: {e}")

    async def _execute_job(self, job_data: Dict[str, Any]):
        """执行任务"""
        job_name = job_data.get('job_name', 'Unknown')
        job_group = job_data.get('job_group', 'DEFAULT')
        invoke_target = job_data.get('invoke_target', '')
        
        start_time = datetime.now()
        status = '0'  # 成功
        job_message = ''
        exception_info = ''
        
        try:
            structured_logger.info(f"开始执行任务: {job_name}")
            
            # 解析并执行目标函数
            func = await self._parse_invoke_target(invoke_target)
            if asyncio.iscoroutinefunction(func):
                await func()
            else:
                func()
            
            job_message = f"任务执行成功"
            structured_logger.info(f"任务执行成功: {job_name}")
            
        except Exception as e:
            status = '1'  # 失败
            job_message = f"任务执行失败: {str(e)}"
            exception_info = traceback.format_exc()
            structured_logger.error(f"任务执行失败: {job_name}, 错误: {e}")
        
        finally:
            # 记录执行日志
            await self._save_job_log(
                job_name=job_name,
                job_group=job_group,
                invoke_target=invoke_target,
                job_message=job_message,
                status=status,
                exception_info=exception_info,
                create_time=start_time
            )

    async def _parse_invoke_target(self, invoke_target: str):
        """解析调用目标"""
        try:
            # 格式: module.function 或 module.class.method
            parts = invoke_target.split('.')
            
            if len(parts) < 2:
                raise ValueError(f"无效的调用目标格式: {invoke_target}")
            
            module_name = '.'.join(parts[:-1])
            func_name = parts[-1]
            
            # 动态导入模块
            module = importlib.import_module(module_name)
            func = getattr(module, func_name)
            
            return func
            
        except Exception as e:
            structured_logger.error(f"解析调用目标失败: {invoke_target}, 错误: {e}")
            raise BusinessException(ErrorCode.VALIDATION_ERROR, f"解析调用目标失败: {e}")

    async def _save_job_log(self, **kwargs):
        """保存任务执行日志"""
        try:
            job_log = SysJobLogModel(**kwargs)
            self.session.add(job_log)
            await self.session.commit()
            
        except Exception as e:
            structured_logger.error(f"保存任务日志失败: {e}")
            await self.session.rollback()
