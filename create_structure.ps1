# 创建server_master目录结构
$baseDir = "server_master"

# 核心框架层目录
$coreDirs = @(
    "$baseDir/core/base",
    "$baseDir/core/database", 
    "$baseDir/core/cache",
    "$baseDir/core/security",
    "$baseDir/core/exceptions",
    "$baseDir/core/logging",
    "$baseDir/core/monitoring",
    "$baseDir/core/middleware",
    "$baseDir/core/mounter",
    "$baseDir/core/scheduler"
)

# 应用模块目录
$appDirs = @(
    "$baseDir/apps/admin/controllers",
    "$baseDir/apps/admin/services", 
    "$baseDir/apps/admin/repositories",
    "$baseDir/apps/admin/models",
    "$baseDir/apps/admin/schemas",
    "$baseDir/apps/admin/crud",
    "$baseDir/apps/admin/routers",
    "$baseDir/apps/admin/annotation",
    "$baseDir/apps/admin/aspect",
    "$baseDir/apps/generator/controllers",
    "$baseDir/apps/generator/services",
    "$baseDir/apps/generator/repositories", 
    "$baseDir/apps/generator/models",
    "$baseDir/apps/generator/schemas",
    "$baseDir/apps/generator/crud",
    "$baseDir/apps/generator/routers",
    "$baseDir/apps/generator/templates",
    "$baseDir/apps/scheduler/controllers",
    "$baseDir/apps/scheduler/services",
    "$baseDir/apps/scheduler/repositories"
)

# 其他目录
$otherDirs = @(
    "$baseDir/config",
    "$baseDir/common", 
    "$baseDir/tests/unit",
    "$baseDir/tests/integration",
    "$baseDir/tests/fixtures",
    "$baseDir/storage/uploads",
    "$baseDir/storage/downloads", 
    "$baseDir/storage/generated",
    "$baseDir/logs",
    "$baseDir/scripts",
    "$baseDir/assets/font"
)

# 创建所有目录
$allDirs = $coreDirs + $appDirs + $otherDirs

foreach ($dir in $allDirs) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
    Write-Host "Created: $dir"
}

Write-Host "Directory structure created successfully!"
