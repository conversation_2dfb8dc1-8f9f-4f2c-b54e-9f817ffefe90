"""
统一错误码枚举模块
"""
from enum import Enum
from typing import Any


class ErrorCode(Enum):
    """统一错误码枚举"""

    # 系统级错误 (1000-1999)
    SYSTEM_ERROR = (1000, "系统内部错误")
    DATABASE_ERROR = (1001, "数据库操作失败")
    CACHE_ERROR = (1002, "缓存操作失败")
    NETWORK_ERROR = (1003, "网络连接异常")
    CONFIG_ERROR = (1004, "配置错误")
    SERVICE_UNAVAILABLE = (1005, "服务不可用")

    # 认证授权错误 (2000-2999)
    UNAUTHORIZED = (2000, "未认证用户")
    TOKEN_EXPIRED = (2001, "令牌已过期")
    TOKEN_INVALID = (2002, "令牌无效")
    PERMISSION_DENIED = (2003, "权限不足")
    LOGIN_FAILED = (2004, "登录失败")
    PASSWORD_INCORRECT = (2005, "密码错误")
    ACCOUNT_LOCKED = (2006, "账户已锁定")
    ACCOUNT_DISABLED = (2007, "账户已禁用")

    # 业务逻辑错误 (3000-3999)
    VALIDATION_ERROR = (3000, "数据验证失败")
    RESOURCE_NOT_FOUND = (3001, "资源不存在")
    RESOURCE_ALREADY_EXISTS = (3002, "资源已存在")
    OPERATION_NOT_ALLOWED = (3003, "操作不被允许")
    BUSINESS_RULE_VIOLATION = (3004, "违反业务规则")
    DATA_INTEGRITY_ERROR = (3005, "数据完整性错误")
    CONCURRENT_MODIFICATION = (3006, "并发修改冲突")

    # 客户端错误 (4000-4999)
    BAD_REQUEST = (4000, "请求参数错误")
    RATE_LIMIT_EXCEEDED = (4001, "请求频率超限")
    FILE_TOO_LARGE = (4002, "文件大小超限")
    INVALID_FILE_TYPE = (4003, "文件类型不支持")
    MISSING_REQUIRED_FIELD = (4004, "缺少必填字段")
    INVALID_FORMAT = (4005, "格式错误")
    DUPLICATE_ENTRY = (4006, "重复条目")

    # 外部服务错误 (5000-5999)
    EXTERNAL_SERVICE_ERROR = (5000, "外部服务错误")
    THIRD_PARTY_API_ERROR = (5001, "第三方API错误")
    EMAIL_SERVICE_ERROR = (5002, "邮件服务错误")
    SMS_SERVICE_ERROR = (5003, "短信服务错误")
    PAYMENT_SERVICE_ERROR = (5004, "支付服务错误")

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

    def __str__(self):
        return f"[{self.code}] {self.message}"

    def __repr__(self):
        return f"ErrorCode.{self.name}({self.code}, '{self.message}')"


class ErrorCodeRegistry:
    """错误码注册表"""

    _custom_codes = {}

    @classmethod
    def register_custom_code(cls, name: str, code: int, message: str):
        """注册自定义错误码"""
        if code in [ec.code for ec in ErrorCode]:
            raise ValueError(f"错误码 {code} 已存在")
        
        cls._custom_codes[name] = (code, message)

    @classmethod
    def get_custom_code(cls, name: str):
        """获取自定义错误码"""
        if name not in cls._custom_codes:
            raise ValueError(f"自定义错误码 {name} 不存在")
        
        code, message = cls._custom_codes[name]
        return type('CustomErrorCode', (), {'code': code, 'message': message})()

    @classmethod
    def get_all_codes(cls):
        """获取所有错误码"""
        codes = {ec.name: (ec.code, ec.message) for ec in ErrorCode}
        codes.update(cls._custom_codes)
        return codes


# 错误码分类
class ErrorCategory:
    """错误码分类"""
    
    SYSTEM = [ec for ec in ErrorCode if 1000 <= ec.code < 2000]
    AUTH = [ec for ec in ErrorCode if 2000 <= ec.code < 3000]
    BUSINESS = [ec for ec in ErrorCode if 3000 <= ec.code < 4000]
    CLIENT = [ec for ec in ErrorCode if 4000 <= ec.code < 5000]
    EXTERNAL = [ec for ec in ErrorCode if 5000 <= ec.code < 6000]

    @classmethod
    def get_category(cls, error_code: ErrorCode) -> str:
        """获取错误码分类"""
        if error_code in cls.SYSTEM:
            return "SYSTEM"
        elif error_code in cls.AUTH:
            return "AUTH"
        elif error_code in cls.BUSINESS:
            return "BUSINESS"
        elif error_code in cls.CLIENT:
            return "CLIENT"
        elif error_code in cls.EXTERNAL:
            return "EXTERNAL"
        else:
            return "UNKNOWN"

    @classmethod
    def is_client_error(cls, error_code: ErrorCode) -> bool:
        """判断是否为客户端错误"""
        return error_code.code < 5000

    @classmethod
    def is_server_error(cls, error_code: ErrorCode) -> bool:
        """判断是否为服务器错误"""
        return error_code.code >= 5000


# 常用错误码快捷访问
class CommonErrors:
    """常用错误码快捷访问"""
    
    # 系统错误
    INTERNAL_ERROR = ErrorCode.SYSTEM_ERROR
    DB_ERROR = ErrorCode.DATABASE_ERROR
    
    # 认证错误
    UNAUTHORIZED = ErrorCode.UNAUTHORIZED
    FORBIDDEN = ErrorCode.PERMISSION_DENIED
    
    # 业务错误
    NOT_FOUND = ErrorCode.RESOURCE_NOT_FOUND
    ALREADY_EXISTS = ErrorCode.RESOURCE_ALREADY_EXISTS
    VALIDATION_FAILED = ErrorCode.VALIDATION_ERROR
    
    # 客户端错误
    BAD_REQUEST = ErrorCode.BAD_REQUEST
    RATE_LIMITED = ErrorCode.RATE_LIMIT_EXCEEDED
