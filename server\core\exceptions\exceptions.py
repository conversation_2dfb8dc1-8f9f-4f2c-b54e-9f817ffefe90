"""
自定义异常类模块
"""

class LoginException(Exception):
    """
    自定义登录异常，用于处理与登录相关的错误。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message


class AuthException(Exception):
    """
    自定义令牌异常，用于处理认证相关的错误。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message


class PermissionException(Exception):
    """
    自定义权限异常，用于处理权限不足或访问拒绝的错误。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message


class ServiceException(Exception):
    """
    自定义服务异常，用于处理业务逻辑中的服务错误。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message


class ServiceWarning(Exception):
    """
    自定义服务警告，用于处理非致命的业务警告信息。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message


class ModelValidatorException(Exception):
    """
    自定义模型校验异常，用于处理模型验证失败的情况。

    :param data: 异常数据（可选）
    :param message: 异常信息（可选）
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message