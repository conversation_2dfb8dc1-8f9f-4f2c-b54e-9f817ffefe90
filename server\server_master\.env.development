# 开发环境配置

# 应用配置
APP_NAME=MXTT-FastAPI
APP_VERSION=2.0.0
APP_ENVIRONMENT=development
APP_DEBUG=true
APP_HOST=0.0.0.0
APP_PORT=9099
APP_SECRET_KEY=your-development-secret-key-change-in-production

# 数据库配置
DB_TYPE=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=1234
DB_DATABASE=ruoyi-fastapi
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=50

# JWT配置
APP_JWT_ALGORITHM=HS256
APP_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
APP_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 日志配置
APP_LOG_LEVEL=DEBUG
APP_LOG_FORMAT=json

# CORS配置
APP_CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
APP_ENABLE_CORS=true

# 限流配置
APP_RATE_LIMIT_ENABLED=true
APP_DEFAULT_RATE_LIMIT=1000
APP_DEFAULT_RATE_WINDOW=60

# 监控配置
APP_ENABLE_METRICS=true
APP_ENABLE_PERFORMANCE_MONITORING=true

# 调度器配置
APP_SCHEDULER_ENABLED=true
APP_SCHEDULER_TIMEZONE=Asia/Shanghai

# 文件上传配置
APP_UPLOAD_MAX_SIZE=10485760
APP_UPLOAD_PATH=storage/uploads
