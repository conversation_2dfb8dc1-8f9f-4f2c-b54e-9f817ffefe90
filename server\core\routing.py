"""
路由注册器模块
"""
from fastapi import FastAPI


class RouterRegistry:
    """路由注册器"""
    
    @classmethod
    def register_all_routers(cls, app: FastAPI):
        """自动发现并注册所有路由"""
        router_configs = cls._get_router_configs()
        
        for config in router_configs:
            app.include_router(
                router=config['router'],
                tags=config['tags']
            )
    
    @classmethod
    def _get_router_configs(cls):
        """获取路由配置列表"""
        # 延迟导入避免循环依赖
        from apps.admin.routers.cache import router as cache_router
        from apps.admin.routers.captcha import router as captcha_router
        from apps.admin.routers.common import router as common_router
        from apps.admin.routers.config import router as config_router
        from apps.admin.routers.dept import router as dept_router
        from apps.admin.routers.dict import router as dict_router
        from apps.admin.routers.log import router as log_router
        from apps.admin.routers.login import router as login_router
        from apps.admin.routers.job import router as job_router
        from apps.admin.routers.menu import router as menu_router
        from apps.admin.routers.notice import router as notice_router
        from apps.admin.routers.online import router as online_router
        from apps.admin.routers.post import router as post_router
        from apps.admin.routers.role import router as role_router
        from apps.admin.routers.server import router as server_router
        from apps.admin.routers.user import router as user_router
        from apps.generator.routers.gen import router as gen_router
        
        return [
            {'router': login_router, 'tags': ['登录模块']},
            {'router': captcha_router, 'tags': ['验证码模块']},
            {'router': user_router, 'tags': ['系统管理-用户管理']},
            {'router': role_router, 'tags': ['系统管理-角色管理']},
            {'router': menu_router, 'tags': ['系统管理-菜单管理']},
            {'router': dept_router, 'tags': ['系统管理-部门管理']},
            {'router': post_router, 'tags': ['系统管理-岗位管理']},
            {'router': dict_router, 'tags': ['系统管理-字典管理']},
            {'router': config_router, 'tags': ['系统管理-参数管理']},
            {'router': notice_router, 'tags': ['系统管理-通知公告管理']},
            {'router': log_router, 'tags': ['系统管理-日志管理']},
            {'router': online_router, 'tags': ['系统监控-在线用户']},
            {'router': job_router, 'tags': ['系统监控-定时任务']},
            {'router': server_router, 'tags': ['系统监控-服务器监控']},
            {'router': cache_router, 'tags': ['系统监控-缓存监控']},
            {'router': common_router, 'tags': ['通用模块']},
            {'router': gen_router, 'tags': ['代码生成']},
        ]
