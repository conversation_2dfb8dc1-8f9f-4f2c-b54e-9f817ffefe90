"""
基础控制器抽象类模块
"""
from abc import ABC, abstractmethod
from typing import Generic, TypeVar, Optional, List, Dict, Any, Union
from fastapi import HTTPException, status, Depends, Query
from pydantic import BaseModel

from ..exceptions.error_codes import <PERSON>rrorCode
from ..exceptions.exceptions import StandardException
from ..logging.structured_logger import structured_logger

T = TypeVar('T')
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)
ResponseSchemaType = TypeVar('ResponseSchemaType', bound=BaseModel)


class BaseController(Generic[T, CreateSchemaType, UpdateSchemaType, ResponseSchemaType], ABC):
    """基础控制器抽象类"""

    def __init__(self, service):
        self.service = service

    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> ResponseSchemaType:
        """创建对象"""
        pass

    @abstractmethod
    async def get_by_id(self, id: Union[int, str]) -> ResponseSchemaType:
        """根据ID获取对象"""
        pass

    @abstractmethod
    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> ResponseSchemaType:
        """更新对象"""
        pass

    @abstractmethod
    async def delete(self, id: Union[int, str]) -> Dict[str, Any]:
        """删除对象"""
        pass

    @abstractmethod
    async def list(
        self,
        page_num: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(10, ge=1, le=100, description="每页数量"),
        **filters
    ) -> Dict[str, Any]:
        """获取对象列表"""
        pass

    def _handle_exception(self, e: Exception, operation: str):
        """统一异常处理"""
        structured_logger.exception(f"控制器操作失败: {operation}", error=str(e))
        
        if isinstance(e, StandardException):
            raise HTTPException(
                status_code=400 if e.error_code.code < 5000 else 500,
                detail=e.detail
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="内部服务器错误"
            )

    def _validate_pagination(self, page_num: int, page_size: int):
        """验证分页参数"""
        if page_num < 1:
            raise StandardException(ErrorCode.BAD_REQUEST, "页码必须大于0")
        if page_size < 1 or page_size > 100:
            raise StandardException(ErrorCode.BAD_REQUEST, "每页数量必须在1-100之间")

    def _calculate_pagination(self, page_num: int, page_size: int):
        """计算分页参数"""
        skip = (page_num - 1) * page_size
        return skip, page_size


class CRUDController(BaseController[T, CreateSchemaType, UpdateSchemaType, ResponseSchemaType]):
    """CRUD控制器基类"""

    async def create(self, obj_in: CreateSchemaType) -> ResponseSchemaType:
        """创建对象"""
        try:
            result = await self.service.create(obj_in)
            structured_logger.info(f"成功创建对象: {type(result).__name__}")
            return self._to_response_schema(result)
        except Exception as e:
            self._handle_exception(e, "create")

    async def get_by_id(self, id: Union[int, str]) -> ResponseSchemaType:
        """根据ID获取对象"""
        try:
            result = await self.service.get_by_id(id)
            if not result:
                raise StandardException(ErrorCode.RESOURCE_NOT_FOUND, f"对象不存在: {id}")
            
            structured_logger.info(f"成功获取对象: {id}")
            return self._to_response_schema(result)
        except Exception as e:
            self._handle_exception(e, f"get_by_id({id})")

    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> ResponseSchemaType:
        """更新对象"""
        try:
            result = await self.service.update(id, obj_in)
            if not result:
                raise StandardException(ErrorCode.RESOURCE_NOT_FOUND, f"对象不存在: {id}")
            
            structured_logger.info(f"成功更新对象: {id}")
            return self._to_response_schema(result)
        except Exception as e:
            self._handle_exception(e, f"update({id})")

    async def delete(self, id: Union[int, str]) -> Dict[str, Any]:
        """删除对象"""
        try:
            success = await self.service.delete(id)
            if not success:
                raise StandardException(ErrorCode.RESOURCE_NOT_FOUND, f"对象不存在: {id}")
            
            structured_logger.info(f"成功删除对象: {id}")
            return {"success": True, "message": "删除成功"}
        except Exception as e:
            self._handle_exception(e, f"delete({id})")

    async def list(
        self,
        page_num: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(10, ge=1, le=100, description="每页数量"),
        **filters
    ) -> Dict[str, Any]:
        """获取对象列表"""
        try:
            self._validate_pagination(page_num, page_size)
            skip, limit = self._calculate_pagination(page_num, page_size)
            
            # 过滤掉None值
            clean_filters = {k: v for k, v in filters.items() if v is not None}
            
            items = await self.service.list(skip=skip, limit=limit, filters=clean_filters)
            total = await self.service.count(filters=clean_filters)
            
            structured_logger.info(f"成功获取对象列表: 页码={page_num}, 数量={len(items)}")
            
            return {
                "rows": [self._to_response_schema(item) for item in items],
                "total": total,
                "page_num": page_num,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        except Exception as e:
            self._handle_exception(e, "list")

    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[ResponseSchemaType]:
        """批量创建对象"""
        try:
            results = await self.service.bulk_create(objs_in)
            structured_logger.info(f"成功批量创建对象: {len(results)}个")
            return [self._to_response_schema(result) for result in results]
        except Exception as e:
            self._handle_exception(e, "bulk_create")

    async def bulk_update(self, updates: List[Dict[str, Any]]) -> List[ResponseSchemaType]:
        """批量更新对象"""
        try:
            results = await self.service.bulk_update(updates)
            structured_logger.info(f"成功批量更新对象: {len(results)}个")
            return [self._to_response_schema(result) for result in results]
        except Exception as e:
            self._handle_exception(e, "bulk_update")

    async def bulk_delete(self, ids: List[Union[int, str]]) -> Dict[str, Any]:
        """批量删除对象"""
        try:
            deleted_count = await self.service.bulk_delete(ids)
            structured_logger.info(f"成功批量删除对象: {deleted_count}个")
            return {"success": True, "deleted_count": deleted_count}
        except Exception as e:
            self._handle_exception(e, "bulk_delete")

    @abstractmethod
    def _to_response_schema(self, obj: T) -> ResponseSchemaType:
        """转换为响应模式"""
        pass


class BusinessController(BaseController[T, CreateSchemaType, UpdateSchemaType, ResponseSchemaType]):
    """业务控制器基类，用于复杂业务逻辑"""

    def __init__(self, service):
        super().__init__(service)
        self._validators = {}

    def add_validator(self, operation: str, validator):
        """添加验证器"""
        self._validators[operation] = validator

    async def _validate_operation(self, operation: str, data: Any):
        """验证操作"""
        validator = self._validators.get(operation)
        if validator:
            await validator(data)

    async def _execute_with_transaction(self, operation):
        """在事务中执行操作"""
        async with await self.service.begin_transaction():
            try:
                result = await operation()
                await self.service.commit_transaction()
                return result
            except Exception:
                await self.service.rollback_transaction()
                raise
