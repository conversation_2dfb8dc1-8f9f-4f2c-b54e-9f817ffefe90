import io
import os
import pandas as pd
import re
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from sqlalchemy.engine.row import Row
from sqlalchemy.orm.collections import InstrumentedList
from typing import Any, Dict, List, Literal, Union
from core.database.database_manager import Base
from config import settings


# 删除worship函数，不是必要的业务逻辑


class SqlalchemyService:
    """
    sqlalchemy工具类
    """

    @classmethod
    def base_to_dict(
            cls, obj: Union[Base, Dict],
            transform_case: Literal['no_case', 'snake_to_camel', 'camel_to_snake'] = 'no_case'
    ):
        """
        将sqlalchemy模型对象转换为字典

        :param obj: sqlalchemy模型对象或普通字典
        :param transform_case: 转换得到的结果形式，可选的有'no_case'(不转换)、'snake_to_camel'(下划线转小驼峰)、'camel_to_snake'(小驼峰转下划线)，默认为'no_case'
        :return: 字典结果
        """
        if isinstance(obj, Base):
            base_dict = obj.__dict__.copy()
            base_dict.pop('_sa_instance_state', None)

            # 根据transform_case参数转换字段名和值
            result = {}
            for k, v in base_dict.items():
                if k == '_sa_instance_state':
                    continue

                # 转换字段名
                if transform_case == 'snake_to_camel':
                    new_key = CamelCaseService.snake_to_camel(k)
                elif transform_case == 'camel_to_snake':
                    new_key = SnakeCaseService.camel_to_snake(k)
                else:
                    new_key = k

                # 转换字段值
                if isinstance(v, InstrumentedList):
                    new_value = cls.serialize_result(v, transform_case)
                else:
                    new_value = v

                result[new_key] = new_value

            return result

        return obj if isinstance(obj, dict) else {}

    @classmethod
    def serialize_result(
            cls, result: Any, transform_case: Literal['no_case', 'snake_to_camel', 'camel_to_snake'] = 'no_case'
    ):
        """
        将sqlalchemy查询结果序列化

        :param result: sqlalchemy查询结果
        :param transform_case: 转换得到的结果形式，可选的有'no_case'(不转换)、'snake_to_camel'(下划线转小驼峰)、'camel_to_snake'(小驼峰转下划线)，默认为'no_case'
        :return: 序列化结果
        """
        # 使用match-case语法替代多重if判断，提高代码可读性
        match result:
            case Base() | dict():
                return cls.base_to_dict(result, transform_case)
            case list():
                return [cls.serialize_result(row, transform_case) for row in result]
            case Row():
                # 简化Row类型的处理逻辑
                result_dict = result._asdict()
                return {
                    CamelCaseService.snake_to_camel(k) if transform_case == 'snake_to_camel' 
                    else SnakeCaseService.camel_to_snake(k) if transform_case == 'camel_to_snake' 
                    else k: v 
                    for k, v in result_dict.items()
                }
            case _:
                return result


class CamelCaseService:
    """
    下划线形式(snake_case)转小驼峰形式(camelCase)工具方法
    """

    @classmethod
    def snake_to_camel(cls, snake_str: str):
        """
        下划线形式字符串(snake_case)转换为小驼峰形式字符串(camelCase)

        :param snake_str: 下划线形式字符串
        :return: 小驼峰形式字符串
        """
        # 分割字符串
        words = snake_str.split('_')
        # 小驼峰命名，第一个词首字母小写，其余词首字母大写
        return words[0] + ''.join(word.capitalize() for word in words[1:])

    @classmethod
    def transform_result(cls, result: Any):
        """
        针对不同类型将下划线形式(snake_case)批量转换为小驼峰形式(camelCase)方法

        :param result: 输入数据
        :return: 小驼峰形式结果
        """
        return SqlalchemyService.serialize_result(result=result, transform_case='snake_to_camel')


class SnakeCaseService:
    """
    小驼峰形式(camelCase)转下划线形式(snake_case)工具方法
    """

    @classmethod
    def camel_to_snake(cls, camel_str: str):
        """
        小驼峰形式字符串(camelCase)转换为下划线形式字符串(snake_case)

        :param camel_str: 小驼峰形式字符串
        :return: 下划线形式字符串
        """
        # 在大写字母前添加一个下划线，然后将整个字符串转为小写
        words = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', words).lower()

    @classmethod
    def transform_result(cls, result: Any):
        """
        针对不同类型将下划线形式(snake_case)批量转换为小驼峰形式(camelCase)方法

        :param result: 输入数据
        :return: 小驼峰形式结果
        """
        return SqlalchemyService.serialize_result(result=result, transform_case='camel_to_snake')


def bytes2human(n, format_str='%(value).1f%(symbol)s'):
    """Used by various scripts. See:
    http://goo.gl/zeJZl

    >>> bytes2human(10000)
    '9.8K'
    >>> bytes2human(100001221)
    '95.4M'
    """
    symbols = ('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB')
    prefix = {}
    for i, s in enumerate(symbols[1:]):
        prefix[s] = 1 << (i + 1) * 10
    
    # 使用next函数和生成器表达式优化查找逻辑
    symbol = next((s for s in reversed(symbols[1:]) if n >= prefix[s]), None)
    if symbol:
        value = float(n) / prefix[symbol]
        return format_str % locals()
    
    # 默认返回字节单位
    return format_str % dict(symbol=symbols[0], value=n)


def bytes2file_response(bytes_info):
    yield bytes_info


def export_list2excel(list_data: List):
    """
    工具方法：将需要导出的list数据转化为对应excel的二进制数据

    :param list_data: 数据列表
    :return: 字典信息对应excel的二进制数据
    """
    df = pd.DataFrame(list_data)
    binary_data = io.BytesIO()
    df.to_excel(binary_data, index=False, engine='openpyxl')
    binary_data = binary_data.getvalue()

    return binary_data


def get_excel_template(header_list: List, selector_header_list: List, option_list: List[dict]):
    """
    工具方法：将需要导出的list数据转化为对应excel的二进制数据

    :param header_list: 表头数据列表
    :param selector_header_list: 需要设置为选择器格式的表头数据列表
    :param option_list: 选择器格式的表头预设的选项列表
    :return: 模板excel的二进制数据
    """
    # 创建Excel工作簿
    wb = Workbook()
    # 选择默认的活动工作表
    ws = wb.active

    # 设置表头文字
    headers = header_list

    # 设置表头背景样式为灰色，前景色为白色
    header_fill = PatternFill(start_color='ababab', end_color='ababab', fill_type='solid')

    # 将表头写入第一行
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = header
        cell.fill = header_fill
        # 设置列宽度为16
        ws.column_dimensions[get_column_letter(col_num)].width = 12
        # 设置水平居中对齐
        cell.alignment = Alignment(horizontal='center')

    # 设置选择器的预设选项
    options = option_list

    # 获取selector_header的字母索引
    for selector_header in selector_header_list:
        try:
            column_selector_header_index = headers.index(selector_header) + 1
        except ValueError:
            # 如果未找到header，跳过该选择器配置
            continue
            
        # 使用字典推导式优化选项查找
        header_option = {k: v for item in options if selector_header in item for k, v in item.items()}
        
        if header_option:
            # 创建数据有效性规则
            dv = DataValidation(type='list', formula1=f'"{",".join(header_option)}"')
            # 设置数据有效性规则的起始单元格和结束单元格
            column_letter = get_column_letter(column_selector_header_index)
            dv.add(f'{column_letter}2:{column_letter}1048576')
            # 添加数据有效性规则到工作表
            ws.add_data_validation(dv)

    # 保存Excel文件为字节类型的数据
    file = io.BytesIO()
    wb.save(file)
    file.seek(0)

    # 读取字节数据
    excel_data = file.getvalue()

    return excel_data


def get_filepath_from_url(url: str):
    """
    工具方法：根据请求参数获取文件路径

    :param url: 请求参数中的url参数
    :return: 文件路径
    """
    try:
        # 使用字典推导式解析URL参数
        params = {k: v for k, v in [x.split('=') for x in url.split('?')[1].split('&')]}
        
        # 使用os.path模块替代字符串拼接，提高跨平台兼容性
        cache_path = os.path.join(os.getcwd(), 'caches')
        return os.path.join(cache_path, params['taskPath'], params['taskId'], params['fileName'])
    except (IndexError, KeyError) as e:
        # 捕获参数解析异常，返回None表示解析失败
        print(f'URL参数解析失败: {e}')
        return None
