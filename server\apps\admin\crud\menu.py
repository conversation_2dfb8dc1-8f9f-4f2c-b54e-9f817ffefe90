from sqlalchemy import and_, delete, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.menu import SysMenuModel
from apps.admin.models.role import SysRoleModel, SysRoleMenuModel
from apps.admin.models.user import SysUserModel, SysUserRoleModel
from apps.admin.schemas.menu import MenuSchema, MenuQuerySchema


class MenuCrud:
    """
    菜单管理模块数据库操作层
    """

    @classmethod
    async def get_menu_detail_by_id(cls, db: AsyncSession, menu_id: int):
        """
        根据菜单id获取菜单详细信息

        :param db: orm对象
        :param menu_id: 菜单id
        :return: 菜单信息对象
        """
        menu_info = (await db.execute(select(SysMenuModel).where(SysMenuModel.menu_id == menu_id))).scalars().first()

        return menu_info

    @classmethod
    async def get_menu_detail_by_info(cls, db: AsyncSession, menu: MenuSchema):
        """
        根据菜单参数获取菜单信息

        :param db: orm对象
        :param menu: 菜单参数对象
        :return: 菜单信息对象
        """
        menu_info = (
            (
                await db.execute(
                    select(SysMenuModel).where(
                        SysMenuModel.parent_id == menu.parent_id if menu.parent_id else True,
                        SysMenuModel.menu_name == menu.menu_name if menu.menu_name else True,
                        SysMenuModel.menu_type == menu.menu_type if menu.menu_type else True,
                    )
                )
            )
            .scalars()
            .first()
        )

        return menu_info

    @classmethod
    async def get_menu_list_for_tree(cls, db: AsyncSession, user_id: int, role: list):
        """
        根据角色信息获取所有在用菜单列表信息

        :param db: orm对象
        :param user_id: 用户id
        :param role: 用户角色列表信息
        :return: 菜单列表信息
        """
        role_id_list = [item.role_id for item in role]
        if 1 in role_id_list:
            menu_query_all = (
                (await db.execute(select(SysMenuModel).where(SysMenuModel.status == '0').order_by(SysMenuModel.order_num).distinct()))
                .scalars()
                .all()
            )
        else:
            menu_query_all = (
                (
                    await db.execute(
                        select(SysMenuModel)
                        .select_from(SysUserModel)
                        .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                        .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                        .join(
                            SysRoleModel,
                            and_(
                                SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'
                            ),
                            isouter=True,
                        )
                        .join(SysRoleMenuModel, SysRoleModel.role_id == SysRoleMenuModel.role_id, isouter=True)
                        .join(SysMenuModel, and_(SysRoleMenuModel.menu_id == SysMenuModel.menu_id, SysMenuModel.status == '0'))
                        .order_by(SysMenuModel.order_num)
                        .distinct()
                    )
                )
                .scalars()
                .all()
            )

        return menu_query_all

    @classmethod
    async def get_menu_list(cls, db: AsyncSession, page_object: MenuQuerySchema, user_id: int, role: list):
        """
        根据查询参数获取菜单列表信息

        :param db: orm对象
        :param page_object: 不分页查询参数对象
        :param user_id: 用户id
        :param role: 用户角色列表
        :return: 菜单列表信息对象
        """
        role_id_list = [item.role_id for item in role]
        if 1 in role_id_list:
            menu_query_all = (
                (
                    await db.execute(
                        select(SysMenuModel)
                        .where(
                            SysMenuModel.status == page_object.status if page_object.status else True,
                            SysMenuModel.menu_name.like(f'%{page_object.menu_name}%') if page_object.menu_name else True,
                        )
                        .order_by(SysMenuModel.order_num)
                        .distinct()
                    )
                )
                .scalars()
                .all()
            )
        else:
            menu_query_all = (
                (
                    await db.execute(
                        select(SysMenuModel)
                        .select_from(SysUserModel)
                        .where(SysUserModel.status == '0', SysUserModel.del_flag == '0', SysUserModel.user_id == user_id)
                        .join(SysUserRoleModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
                        .join(
                            SysRoleModel,
                            and_(
                                SysUserRoleModel.role_id == SysRoleModel.role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0'
                            ),
                            isouter=True,
                        )
                        .join(SysRoleMenuModel, SysRoleModel.role_id == SysRoleMenuModel.role_id, isouter=True)
                        .join(
                            SysMenuModel,
                            and_(
                                SysRoleMenuModel.menu_id == SysMenuModel.menu_id,
                                SysMenuModel.status == page_object.status if page_object.status else True,
                                SysMenuModel.menu_name.like(f'%{page_object.menu_name}%') if page_object.menu_name else True,
                            ),
                        )
                        .order_by(SysMenuModel.order_num)
                        .distinct()
                    )
                )
                .scalars()
                .all()
            )

        return menu_query_all

    @classmethod
    async def add_menu(cls, db: AsyncSession, menu: MenuSchema):
        """
        新增菜单数据库操作

        :param db: orm对象
        :param menu: 菜单对象
        :return:
        """
        db_menu = SysMenuModel(**menu.model_dump())
        db.add(db_menu)
        await db.flush()

        return db_menu

    @classmethod
    async def edit_menu(cls, db: AsyncSession, menu: dict):
        """
        编辑菜单数据库操作

        :param db: orm对象
        :param menu: 需要更新的菜单字典
        :return:
        """
        await db.execute(update(SysMenuModel), [menu])

    @classmethod
    async def delete_menu(cls, db: AsyncSession, menu: MenuSchema):
        """
        删除菜单数据库操作

        :param db: orm对象
        :param menu: 菜单对象
        :return:
        """
        await db.execute(delete(SysMenuModel).where(SysMenuModel.menu_id.in_([menu.menu_id])))

    @classmethod
    async def has_child_by_menu_id(cls, db: AsyncSession, menu_id: int):
        """
        根据菜单id查询菜单关联子菜单的数量

        :param db: orm对象
        :param menu_id: 菜单id
        :return: 菜单关联子菜单的数量
        """
        menu_count = (
            await db.execute(select(func.count('*')).select_from(SysMenuModel).where(SysMenuModel.parent_id == menu_id))
        ).scalar()

        return menu_count

    @classmethod
    async def check_menu_exist_role(cls, db: AsyncSession, menu_id: int):
        """
        根据菜单id查询菜单关联角色数量

        :param db: orm对象
        :param menu_id: 菜单id
        :return: 菜单关联角色数量
        """
        role_count = (
            await db.execute(select(func.count('*')).select_from(SysRoleMenuModel).where(SysRoleMenuModel.menu_id == menu_id))
        ).scalar()

        return role_count
