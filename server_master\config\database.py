"""
数据库配置模块
"""
from pydantic import computed_field
from pydantic_settings import BaseSettings
from typing import Literal, Dict, Any


class DatabaseConfig(BaseSettings):
    """优化后的数据库配置"""

    # 基础配置
    type: Literal['mysql', 'postgresql', 'sqlite'] = 'mysql'
    host: str = '127.0.0.1'
    port: int = 3306
    username: str = 'root'
    password: str = '1234'
    database: str = 'ruoyi-fastapi'

    # 性能优化配置
    echo: bool = False  # 生产环境关闭SQL日志
    pool_size: int = 20  # 根据并发量调整
    max_overflow: int = 30  # 增加溢出连接
    pool_recycle: int = 1800  # 缩短连接回收时间
    pool_timeout: int = 10  # 缩短超时时间
    pool_pre_ping: bool = True  # 启用连接预检

    # 查询优化
    query_cache_size: int = 1000
    statement_timeout: int = 30000  # 30秒查询超时

    # 连接参数优化
    connect_args: Dict[str, Any] = {
        'charset': 'utf8mb4',
        'autocommit': False,
        'connect_timeout': 10,
        'read_timeout': 30,
        'write_timeout': 30,
    }

    # SSL配置
    ssl_disabled: bool = True
    ssl_ca: str = ''
    ssl_cert: str = ''
    ssl_key: str = ''

    @computed_field
    @property
    def async_database_url(self) -> str:
        """异步数据库连接URL"""
        if self.type == 'mysql':
            driver = 'mysql+asyncmy'
        elif self.type == 'postgresql':
            driver = 'postgresql+asyncpg'
        elif self.type == 'sqlite':
            driver = 'sqlite+aiosqlite'
            return f"{driver}:///{self.database}"
        else:
            raise ValueError(f"不支持的数据库类型: {self.type}")
        
        return f"{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

    @computed_field
    @property
    def sync_database_url(self) -> str:
        """同步数据库连接URL"""
        if self.type == 'mysql':
            driver = 'mysql+pymysql'
        elif self.type == 'postgresql':
            driver = 'postgresql+psycopg2'
        elif self.type == 'sqlite':
            driver = 'sqlite'
            return f"{driver}:///{self.database}"
        else:
            raise ValueError(f"不支持的数据库类型: {self.type}")
        
        return f"{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

    @computed_field
    @property
    def engine_options(self) -> Dict[str, Any]:
        """数据库引擎选项"""
        options = {
            'echo': self.echo,
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_recycle': self.pool_recycle,
            'pool_timeout': self.pool_timeout,
            'pool_pre_ping': self.pool_pre_ping,
        }

        # 添加连接参数
        if self.type in ['mysql', 'postgresql']:
            options['connect_args'] = self.connect_args.copy()
            
            # SSL配置
            if not self.ssl_disabled:
                ssl_args = {}
                if self.ssl_ca:
                    ssl_args['ssl_ca'] = self.ssl_ca
                if self.ssl_cert:
                    ssl_args['ssl_cert'] = self.ssl_cert
                if self.ssl_key:
                    ssl_args['ssl_key'] = self.ssl_key
                
                if ssl_args:
                    options['connect_args'].update(ssl_args)

        return options

    def get_alembic_config(self) -> Dict[str, str]:
        """获取Alembic配置"""
        return {
            'sqlalchemy.url': self.sync_database_url,
            'script_location': 'alembic',
            'file_template': '%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s',
            'timezone': 'UTC'
        }

    class Config:
        env_prefix = 'DB_'
        env_file = '.env'
        case_sensitive = False
