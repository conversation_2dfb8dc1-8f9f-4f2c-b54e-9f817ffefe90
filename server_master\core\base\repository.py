"""
基础仓储抽象类模块
"""
from abc import ABC, abstractmethod
from typing import Generic, TypeVar, Optional, List, Dict, Any, Union, Type
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, delete, update
from sqlalchemy.orm import selectinload, joinedload
from pydantic import BaseModel

ModelType = TypeVar('ModelType')
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """基础仓储抽象类"""

    def __init__(self, session: AsyncSession, model: Type[ModelType]):
        self.session = session
        self.model = model

    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        pass

    @abstractmethod
    async def get_by_id(self, id: Union[int, str]) -> Optional[ModelType]:
        """根据ID获取对象"""
        pass

    @abstractmethod
    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """更新对象"""
        pass

    @abstractmethod
    async def delete(self, id: Union[int, str]) -> bool:
        """删除对象"""
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        """获取对象列表"""
        pass

    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """获取对象总数"""
        query = select(func.count(self.model.id))
        if filters:
            query = self._apply_filters(query, filters)
        result = await self.session.execute(query)
        return result.scalar()

    async def exists(self, id: Union[int, str]) -> bool:
        """检查对象是否存在"""
        query = select(self.model).where(self.model.id == id)
        result = await self.session.execute(query)
        return result.scalar_one_or_none() is not None

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """应用过滤条件"""
        for key, value in filters.items():
            if hasattr(self.model, key):
                if isinstance(value, list):
                    query = query.where(getattr(self.model, key).in_(value))
                elif isinstance(value, dict):
                    # 支持范围查询 {'gte': 10, 'lte': 20}
                    if 'gte' in value:
                        query = query.where(getattr(self.model, key) >= value['gte'])
                    if 'lte' in value:
                        query = query.where(getattr(self.model, key) <= value['lte'])
                    if 'gt' in value:
                        query = query.where(getattr(self.model, key) > value['gt'])
                    if 'lt' in value:
                        query = query.where(getattr(self.model, key) < value['lt'])
                    if 'like' in value:
                        query = query.where(getattr(self.model, key).like(f"%{value['like']}%"))
                else:
                    query = query.where(getattr(self.model, key) == value)
        return query

    def _apply_ordering(self, query, order_by: Optional[str] = None, order_desc: bool = False):
        """应用排序"""
        if order_by and hasattr(self.model, order_by):
            field = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(field.desc())
            else:
                query = query.order_by(field)
        return query


class SQLAlchemyRepository(BaseRepository[ModelType, CreateSchemaType, UpdateSchemaType]):
    """SQLAlchemy仓储实现"""

    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        obj_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in.dict()
        db_obj = self.model(**obj_data)
        self.session.add(db_obj)
        await self.session.flush()
        await self.session.refresh(db_obj)
        return db_obj

    async def get_by_id(self, id: Union[int, str]) -> Optional[ModelType]:
        """根据ID获取对象"""
        query = select(self.model).where(self.model.id == id)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def update(self, id: Union[int, str], obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """更新对象"""
        db_obj = await self.get_by_id(id)
        if not db_obj:
            return None

        obj_data = obj_in.model_dump(exclude_unset=True) if hasattr(obj_in, 'model_dump') else obj_in.dict(exclude_unset=True)
        for field, value in obj_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)

        await self.session.flush()
        await self.session.refresh(db_obj)
        return db_obj

    async def delete(self, id: Union[int, str]) -> bool:
        """删除对象"""
        db_obj = await self.get_by_id(id)
        if not db_obj:
            return False

        await self.session.delete(db_obj)
        await self.session.flush()
        return True

    async def list(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False,
        relationships: Optional[List[str]] = None
    ) -> List[ModelType]:
        """获取对象列表"""
        query = select(self.model)
        
        # 应用过滤条件
        if filters:
            query = self._apply_filters(query, filters)
        
        # 应用排序
        query = self._apply_ordering(query, order_by, order_desc)
        
        # 预加载关系
        if relationships:
            for rel in relationships:
                if hasattr(self.model, rel):
                    query = query.options(selectinload(getattr(self.model, rel)))
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await self.session.execute(query)
        return result.scalars().all()

    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[ModelType]:
        """批量创建对象"""
        db_objs = []
        for obj_in in objs_in:
            obj_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in.dict()
            db_obj = self.model(**obj_data)
            db_objs.append(db_obj)
        
        self.session.add_all(db_objs)
        await self.session.flush()
        
        for db_obj in db_objs:
            await self.session.refresh(db_obj)
        
        return db_objs

    async def bulk_update(self, updates: List[Dict[str, Any]]) -> int:
        """批量更新对象"""
        if not updates:
            return 0

        updated_count = 0
        for update_data in updates:
            obj_id = update_data.pop('id')
            query = update(self.model).where(self.model.id == obj_id).values(**update_data)
            result = await self.session.execute(query)
            updated_count += result.rowcount

        await self.session.flush()
        return updated_count

    async def bulk_delete(self, ids: List[Union[int, str]]) -> int:
        """批量删除对象"""
        if not ids:
            return 0

        query = delete(self.model).where(self.model.id.in_(ids))
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount
