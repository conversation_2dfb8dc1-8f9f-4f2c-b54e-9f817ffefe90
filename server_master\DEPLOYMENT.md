# MXTT-FastAPI 部署指南

## 概述

本文档详细介绍了 MXTT-FastAPI 项目的部署流程，包括开发环境、测试环境和生产环境的部署方案。

## 系统要求

### 硬件要求

**最低配置**
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 网络: 100Mbps

**推荐配置**
- CPU: 4核心以上
- 内存: 8GB RAM以上
- 存储: 50GB SSD
- 网络: 1Gbps

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Docker
- **Python**: 3.11+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **Nginx**: 1.20+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 部署方式

### 1. Docker 部署（推荐）

#### 开发环境部署

```bash
# 克隆项目
git clone <repository-url>
cd server_master

# 配置环境变量
cp .env.development.example .env.development
# 编辑 .env.development 文件

# 启动开发环境
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
./scripts/deploy.sh dev --logs
```

#### 生产环境部署

```bash
# 配置生产环境变量
cp .env.production.example .env.production
# 编辑 .env.production 文件，设置安全的密码和密钥

# 启动生产环境
./scripts/deploy.sh prod --build

# 查看服务状态
docker-compose ps

# 查看日志
./scripts/deploy.sh prod --logs
```

### 2. 手动部署

#### 环境准备

```bash
# 安装 Python 3.11
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 安装 MySQL
sudo apt install mysql-server-8.0
sudo mysql_secure_installation

# 安装 Redis
sudo apt install redis-server

# 安装 Nginx
sudo apt install nginx
```

#### 后端部署

```bash
# 创建项目目录
sudo mkdir -p /opt/mxtt-fastapi
sudo chown $USER:$USER /opt/mxtt-fastapi
cd /opt/mxtt-fastapi

# 克隆代码
git clone <repository-url> .

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.production.example .env.production
# 编辑配置文件

# 数据库初始化
python -c "from core.database.manager import DatabaseManager; import asyncio; asyncio.run(DatabaseManager.create_tables())"

# 创建系统服务
sudo tee /etc/systemd/system/mxtt-fastapi.service > /dev/null <<EOF
[Unit]
Description=MXTT FastAPI Application
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/mxtt-fastapi
Environment=PATH=/opt/mxtt-fastapi/venv/bin
ExecStart=/opt/mxtt-fastapi/venv/bin/uvicorn main:app --host 0.0.0.0 --port 9099 --workers 4
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable mxtt-fastapi
sudo systemctl start mxtt-fastapi
```

#### 前端部署

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 构建前端
cd ../admin
npm install
npm run build:prod

# 复制到 Nginx 目录
sudo cp -r dist/* /var/www/html/
```

#### Nginx 配置

```bash
# 复制 Nginx 配置
sudo cp scripts/nginx/nginx.conf /etc/nginx/nginx.conf
sudo cp scripts/nginx/conf.d/* /etc/nginx/conf.d/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 配置说明

### 环境变量配置

#### 开发环境 (.env.development)

```env
# 应用配置
APP_ENVIRONMENT=development
APP_DEBUG=true
APP_HOST=0.0.0.0
APP_PORT=9099

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=1234
DB_DATABASE=ruoyi-fastapi

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
```

#### 生产环境 (.env.production)

```env
# 应用配置
APP_ENVIRONMENT=production
APP_DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=9099

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=mxtt_user
DB_PASSWORD=your-secure-password
DB_DATABASE=mxtt_production

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# JWT配置
JWT_SECRET_KEY=your-very-secure-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 安全配置
CORS_ORIGINS=["https://yourdomain.com"]
ALLOWED_HOSTS=["yourdomain.com", "www.yourdomain.com"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
```

### 数据库配置

#### MySQL 配置优化

```sql
-- 创建数据库和用户
CREATE DATABASE mxtt_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mxtt_user'@'%' IDENTIFIED BY 'your-secure-password';
GRANT ALL PRIVILEGES ON mxtt_production.* TO 'mxtt_user'@'%';
FLUSH PRIVILEGES;

-- 性能优化配置
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
```

#### Redis 配置优化

```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 监控和维护

### 健康检查

```bash
# 应用健康检查
curl http://localhost:9099/health

# 数据库连接检查
curl http://localhost:9099/health/db

# Redis连接检查
curl http://localhost:9099/health/redis
```

### 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log

# 日志轮转配置
sudo tee /etc/logrotate.d/mxtt-fastapi > /dev/null <<EOF
/opt/mxtt-fastapi/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload mxtt-fastapi
    endscript
}
EOF
```

### 性能监控

#### Prometheus 指标

访问 `http://localhost:9090` 查看 Prometheus 监控面板

#### Grafana 仪表板

访问 `http://localhost:3000` 查看 Grafana 可视化面板
- 默认用户名: admin
- 默认密码: admin123

### 备份策略

#### 数据库备份

```bash
# 创建备份脚本
sudo tee /opt/backup/mysql-backup.sh > /dev/null <<EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backup/mysql"
mkdir -p $BACKUP_DIR

mysqldump -u mxtt_user -p'your-secure-password' mxtt_production > $BACKUP_DIR/mxtt_$DATE.sql
gzip $BACKUP_DIR/mxtt_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x /opt/backup/mysql-backup.sh

# 添加到定时任务
echo "0 2 * * * /opt/backup/mysql-backup.sh" | sudo crontab -
```

#### Redis 备份

```bash
# Redis 自动备份
echo "0 3 * * * cp /var/lib/redis/dump.rdb /opt/backup/redis/dump_$(date +\%Y\%m\%d).rdb" | sudo crontab -
```

## 故障排除

### 常见问题

#### 1. 应用无法启动

```bash
# 检查端口占用
sudo netstat -tlnp | grep :9099

# 检查服务状态
sudo systemctl status mxtt-fastapi

# 查看详细日志
sudo journalctl -u mxtt-fastapi -f
```

#### 2. 数据库连接失败

```bash
# 检查 MySQL 服务
sudo systemctl status mysql

# 测试数据库连接
mysql -u mxtt_user -p -h localhost mxtt_production

# 检查防火墙
sudo ufw status
```

#### 3. Redis 连接失败

```bash
# 检查 Redis 服务
sudo systemctl status redis

# 测试 Redis 连接
redis-cli ping

# 检查 Redis 配置
sudo cat /etc/redis/redis.conf | grep bind
```

#### 4. 前端页面无法访问

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 测试 Nginx 配置
sudo nginx -t

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

### 性能优化

#### 数据库优化

```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析查询性能
EXPLAIN SELECT * FROM sys_user WHERE user_name = 'admin';

-- 添加索引
CREATE INDEX idx_user_name ON sys_user(user_name);
CREATE INDEX idx_create_time ON sys_user(create_time);
```

#### 应用优化

```bash
# 调整 worker 数量
# 编辑 /etc/systemd/system/mxtt-fastapi.service
ExecStart=/opt/mxtt-fastapi/venv/bin/uvicorn main:app --host 0.0.0.0 --port 9099 --workers 8

# 重启服务
sudo systemctl daemon-reload
sudo systemctl restart mxtt-fastapi
```

## 安全加固

### SSL/TLS 配置

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d yourdomain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 防火墙配置

```bash
# 配置 UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 安全更新

```bash
# 自动安全更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 扩展部署

### 负载均衡

使用多个应用实例和 Nginx 负载均衡：

```nginx
upstream fastapi_backend {
    server app1:9099;
    server app2:9099;
    server app3:9099;
}
```

### 数据库集群

配置 MySQL 主从复制或使用 MySQL Cluster。

### Redis 集群

配置 Redis Sentinel 或 Redis Cluster 实现高可用。

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目 Issues
3. 联系技术支持团队

---

**注意**: 请确保在生产环境中使用强密码和安全配置，定期更新系统和依赖包。
