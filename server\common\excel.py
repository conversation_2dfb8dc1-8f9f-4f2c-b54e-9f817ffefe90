import io
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from typing import Dict, List


class ExcelUtil:
    """
    Excel操作类
    """

    @classmethod
    def __mapping_list(cls, list_data: List, mapping_dict: Dict):
        """
        工具方法：将list数据中的字段名映射为对应的中文字段名

        :param list_data: 数据列表
        :param mapping_dict: 映射字典，包含英文字段名到中文字段名的映射关系
        :return: 映射后的数据列表
        """
        # 使用列表推导式和字典推导式简化数据映射过程
        return [{mapping_dict.get(key): item.get(key) for key in mapping_dict if key in item} for item in list_data]

    @classmethod
    def export_list2excel(cls, list_data: List, mapping_dict: Dict):
        """
        工具方法：将需要导出的list数据转化为对应excel的二进制数据

        :param list_data: 数据列表
        :param mapping_dict: 映射字典，包含英文字段名到中文字段名的映射关系
        :return: list数据对应excel的二进制数据
        """
        # 增加异常处理机制，确保输入数据有效性
        try:
            mapping_data = cls.__mapping_list(list_data, mapping_dict)
            df = pd.DataFrame(mapping_data)
            binary_data = io.BytesIO()
            df.to_excel(binary_data, index=False, engine='openpyxl')
            return binary_data.getvalue()
        except Exception as e:
            raise RuntimeError(f'Excel导出失败: {str(e)}')

    @classmethod
    def get_excel_template(cls, header_list: List, selector_header_list: List, option_list: List[Dict]):
        """
        工具方法：生成带有数据验证的Excel模板

        :param header_list: 表头数据列表
        :param selector_header_list: 需要设置为选择器格式的表头数据列表
        :param option_list: 选择器格式的表头预设的选项列表
        :return: 模板excel的二进制数据
        """
        try:
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active

            # 设置表头样式
            header_fill = PatternFill(start_color='ababab', end_color='ababab', fill_type='solid')
            
            # 写入表头并设置样式
            for col_num, header in enumerate(header_list, 1):
                cell = ws.cell(row=1, column=col_num)
                cell.value = header
                cell.fill = header_fill
                ws.column_dimensions[get_column_letter(col_num)].width = 12
                cell.alignment = Alignment(horizontal='center')

            # 处理选择器配置
            for selector_header in selector_header_list:
                try:
                    col_index = header_list.index(selector_header) + 1
                except ValueError:
                    continue  # 如果未找到header，跳过该选择器配置

                # 使用字典推导式优化选项查找
                header_options = {k: v for item in option_list if selector_header in item for k, v in item.items()}
                
                if header_options:
                    # 创建数据有效性规则
                    dv = DataValidation(type='list', formula1=f'"{",".join(header_options)}"')
                    column_letter = get_column_letter(col_index)
                    dv.add(f'{column_letter}2:{column_letter}1048576')
                    ws.add_data_validation(dv)

            # 保存并返回Excel文件的二进制数据
            file = io.BytesIO()
            wb.save(file)
            file.seek(0)
            return file.getvalue()
        except Exception as e:
            raise RuntimeError(f'Excel模板生成失败: {str(e)}')
