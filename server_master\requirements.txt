# MXTT-FastAPI 项目依赖

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
starlette==0.27.0

# 数据库
sqlalchemy[asyncio]==2.0.23
asyncmy==0.2.9
aiomysql==0.2.0
alembic==1.12.1

# 缓存
redis==5.0.1
aioredis==2.0.1

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# 任务调度
apscheduler==3.10.4

# 日志
loguru==0.7.2

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 模板引擎
jinja2==3.1.2

# Excel处理
openpyxl==3.1.2
pandas==2.1.4
xlsxwriter==3.1.9

# 图像处理
pillow==10.1.0

# 系统监控
psutil==5.9.6

# 工具库
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 性能测试
locust==2.17.0

# 类型提示
typing-extensions==4.8.0

# 异步支持
anyio==4.1.0
asyncio-mqtt==0.16.1

# 配置管理
python-dotenv==1.0.0

# 文件处理
aiofiles==23.2.1

# 网络工具
dnspython==2.4.2

# 数据库迁移
yoyo-migrations==8.2.0

# 监控和指标
prometheus-client==0.19.0

# 邮件发送
aiosmtplib==3.0.1

# 时间处理
arrow==1.3.0

# 字符串处理
unidecode==1.3.7

# 加密解密
pycryptodome==3.19.0

# 二维码生成
qrcode[pil]==7.4.2

# 条形码生成
python-barcode==0.15.1

# PDF处理
reportlab==4.0.7

# 地理位置
geoip2==4.7.0

# 消息队列
celery==5.3.4

# 缓存装饰器
cachetools==5.3.2

# 数据压缩
zstandard==0.22.0

# 文件类型检测
python-magic==0.4.27

# 正则表达式增强
regex==2023.10.3

# 字符编码检测
chardet==5.2.0

# URL处理
yarl==1.9.3

# 异步锁
aiolock==1.4.0

# 限流
slowapi==0.1.9

# CORS支持
fastapi-cors==0.0.6

# 中间件
fastapi-limiter==0.1.5

# 数据库连接池
asyncpg==0.29.0

# 分布式锁
redlock-py==1.0.8

# 配置验证
cerberus==1.3.5

# 数据序列化
marshmallow==3.20.1

# API文档增强
fastapi-users==12.1.2

# 健康检查
fastapi-health==0.4.0

# 请求ID追踪
fastapi-request-id==1.0.0

# 异步任务
dramatiq==1.15.0

# 分页支持
fastapi-pagination==0.12.13

# 文件上传
fastapi-uploads==0.1.0

# WebSocket支持
websockets==12.0

# 事件总线
pydispatcher==2.0.7

# 数据验证增强
validators==0.22.0

# 国际化
babel==2.13.1

# 时区处理
zoneinfo==0.2.1; python_version < "3.9"

# 开发服务器
watchfiles==0.21.0

# 代码格式化
autopep8==2.0.4

# 文档生成
sphinx==7.2.6

# 测试覆盖率
coverage==7.3.2

# 内存分析
memory-profiler==0.61.0

# 性能分析
py-spy==0.3.14

# 调试工具
pdb++==0.10.3

# 环境管理
python-decouple==3.8
