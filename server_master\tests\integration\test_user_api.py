"""
用户API集成测试
"""
import pytest
from httpx import AsyncClient


class TestUserAPI:
    """用户API测试类"""

    async def test_create_user_api(self, authenticated_client: AsyncClient, sample_user_data):
        """测试创建用户API"""
        response = await authenticated_client.post("/api/system/user/", json=sample_user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data

    async def test_create_user_invalid_data(self, authenticated_client: AsyncClient):
        """测试创建用户时数据无效"""
        invalid_data = {
            "user_name": "",  # 用户名为空
            "email": "invalid-email"  # 邮箱格式错误
        }
        
        response = await authenticated_client.post("/api/system/user/", json=invalid_data)
        
        assert response.status_code == 422  # 验证错误

    async def test_get_user_list_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试获取用户列表API"""
        # 创建测试数据
        await test_data_factory.create_user(db_session, user_name="test_user1")
        await test_data_factory.create_user(db_session, user_name="test_user2")
        
        response = await authenticated_client.get("/api/system/user/list")
        
        assert response.status_code == 200
        data = response.json()
        assert "rows" in data
        assert "total" in data
        assert data["total"] >= 2

    async def test_get_user_list_with_pagination(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试分页获取用户列表"""
        # 创建多个测试用户
        for i in range(15):
            await test_data_factory.create_user(db_session, user_name=f"test_user_{i}")
        
        # 测试第一页
        response = await authenticated_client.get("/api/system/user/list?pageNum=1&pageSize=10")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["rows"]) == 10
        assert data["total"] >= 15

    async def test_get_user_list_with_filters(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试带过滤条件的用户列表查询"""
        # 创建测试数据
        await test_data_factory.create_user(db_session, user_name="admin_user", nick_name="管理员")
        await test_data_factory.create_user(db_session, user_name="normal_user", nick_name="普通用户")
        
        # 按用户名过滤
        response = await authenticated_client.get("/api/system/user/list?userName=admin")
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] >= 1
        
        # 验证返回的用户包含过滤关键字
        for user in data["rows"]:
            assert "admin" in user["userName"].lower()

    async def test_get_user_detail_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试获取用户详情API"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        response = await authenticated_client.get(f"/api/system/user/{user.user_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["userId"] == user.user_id

    async def test_get_user_detail_not_found(self, authenticated_client: AsyncClient):
        """测试获取不存在用户的详情"""
        response = await authenticated_client.get("/api/system/user/99999")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False

    async def test_update_user_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试更新用户API"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        update_data = {
            "nickName": "更新的昵称",
            "email": "<EMAIL>",
            "phonenumber": "13900139000"
        }
        
        response = await authenticated_client.put(f"/api/system/user/{user.user_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_update_user_not_found(self, authenticated_client: AsyncClient):
        """测试更新不存在的用户"""
        update_data = {
            "nickName": "更新的昵称"
        }
        
        response = await authenticated_client.put("/api/system/user/99999", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False

    async def test_delete_user_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试删除用户API"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        response = await authenticated_client.delete(f"/api/system/user/{user.user_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_delete_admin_user_forbidden(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试删除管理员用户被禁止"""
        # 创建管理员用户
        admin_user = await test_data_factory.create_user(
            db_session, 
            user_name="admin", 
            user_id=1
        )
        
        response = await authenticated_client.delete(f"/api/system/user/{admin_user.user_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False

    async def test_batch_delete_users_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试批量删除用户API"""
        # 创建测试用户
        user1 = await test_data_factory.create_user(db_session, user_name="test_user1")
        user2 = await test_data_factory.create_user(db_session, user_name="test_user2")
        
        user_ids = [user1.user_id, user2.user_id]
        
        response = await authenticated_client.delete("/api/system/user/batch", json=user_ids)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_check_username_unique_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试检查用户名唯一性API"""
        # 创建测试用户
        await test_data_factory.create_user(db_session, user_name="existing_user")
        
        # 检查已存在的用户名
        response = await authenticated_client.get("/api/system/user/check/username?username=existing_user")
        
        assert response.status_code == 200
        data = response.json()
        assert data["unique"] is False
        
        # 检查不存在的用户名
        response = await authenticated_client.get("/api/system/user/check/username?username=new_user")
        
        assert response.status_code == 200
        data = response.json()
        assert data["unique"] is True

    async def test_check_email_unique_api(self, authenticated_client: AsyncClient, test_data_factory, db_session):
        """测试检查邮箱唯一性API"""
        # 创建测试用户
        await test_data_factory.create_user(db_session, email="<EMAIL>")
        
        # 检查已存在的邮箱
        response = await authenticated_client.get("/api/system/user/check/email?email=<EMAIL>")
        
        assert response.status_code == 200
        data = response.json()
        assert data["unique"] is False
        
        # 检查不存在的邮箱
        response = await authenticated_client.get("/api/system/user/check/email?email=<EMAIL>")
        
        assert response.status_code == 200
        data = response.json()
        assert data["unique"] is True

    async def test_unauthorized_access(self, client: AsyncClient):
        """测试未认证访问"""
        response = await client.get("/api/system/user/list")
        
        assert response.status_code == 401

    async def test_user_api_error_handling(self, authenticated_client: AsyncClient):
        """测试API错误处理"""
        # 测试无效的用户ID格式
        response = await authenticated_client.get("/api/system/user/invalid_id")
        
        assert response.status_code == 422  # 验证错误

    async def test_user_api_rate_limiting(self, authenticated_client: AsyncClient):
        """测试API限流（如果启用）"""
        # 这个测试需要根据实际的限流配置来调整
        # 快速发送多个请求
        responses = []
        for _ in range(100):
            response = await authenticated_client.get("/api/system/user/list")
            responses.append(response)
        
        # 检查是否有限流响应
        rate_limited = any(r.status_code == 429 for r in responses)
        
        # 如果启用了限流，应该有429响应
        # 如果没有启用限流，所有响应都应该是200
        success_responses = [r for r in responses if r.status_code == 200]
        assert len(success_responses) > 0
