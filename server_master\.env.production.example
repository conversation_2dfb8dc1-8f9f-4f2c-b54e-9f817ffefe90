# 生产环境配置
APP_ENVIRONMENT=production
APP_DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=9099
APP_SECRET_KEY=your-very-secure-secret-key

# 数据库配置
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=mxtt_user
DB_PASSWORD=your-secure-password
DB_DATABASE=mxtt_production

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DATABASE=0

# JWT配置
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 安全配置
CORS_ORIGINS=["https://yourdomain.com"]
ALLOWED_HOSTS=["yourdomain.com"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
