"""
健康检查模块
"""
import asyncio
import psutil
from typing import Dict, Any, List, Callable, Optional
from enum import Enum
from datetime import datetime
from sqlalchemy import text

from ..logging.structured_logger import structured_logger
from ..database.database_manager import DatabaseManager
from ..cache.cache_manager import CacheManager


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.check_timeout = 10  # 默认10秒超时

    def register_check(self, name: str, check_func: Callable, timeout: int = None):
        """注册健康检查"""
        self.checks[name] = {
            'func': check_func,
            'timeout': timeout or self.check_timeout
        }
        structured_logger.info(f"注册健康检查: {name}")

    def unregister_check(self, name: str):
        """取消注册健康检查"""
        if name in self.checks:
            del self.checks[name]
            structured_logger.info(f"取消注册健康检查: {name}")

    async def check_all(self) -> Dict[str, Any]:
        """执行所有健康检查"""
        results = {}
        overall_status = HealthStatus.HEALTHY
        start_time = datetime.now()

        for name, check_config in self.checks.items():
            try:
                check_func = check_config['func']
                timeout = check_config['timeout']
                
                # 执行健康检查，带超时
                result = await asyncio.wait_for(check_func(), timeout=timeout)
                results[name] = result

                # 更新整体状态
                if result['status'] == HealthStatus.UNHEALTHY.value:
                    overall_status = HealthStatus.UNHEALTHY
                elif result['status'] == HealthStatus.DEGRADED.value and overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED

                structured_logger.debug(f"健康检查完成: {name}, 状态: {result['status']}")

            except asyncio.TimeoutError:
                results[name] = {
                    'status': HealthStatus.UNHEALTHY.value,
                    'error': f'健康检查超时 ({timeout}秒)',
                    'timestamp': datetime.now().isoformat()
                }
                overall_status = HealthStatus.UNHEALTHY
                structured_logger.error(f"健康检查超时: {name}")

            except Exception as e:
                results[name] = {
                    'status': HealthStatus.UNHEALTHY.value,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                overall_status = HealthStatus.UNHEALTHY
                structured_logger.error(f"健康检查异常: {name}, 错误: {e}")

        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()

        health_report = {
            'status': overall_status.value,
            'timestamp': end_time.isoformat(),
            'duration': total_duration,
            'checks': results,
            'summary': {
                'total_checks': len(self.checks),
                'healthy_checks': sum(1 for r in results.values() if r['status'] == HealthStatus.HEALTHY.value),
                'degraded_checks': sum(1 for r in results.values() if r['status'] == HealthStatus.DEGRADED.value),
                'unhealthy_checks': sum(1 for r in results.values() if r['status'] == HealthStatus.UNHEALTHY.value)
            }
        }

        structured_logger.info(
            f"健康检查完成",
            overall_status=overall_status.value,
            duration=total_duration,
            total_checks=len(self.checks)
        )

        return health_report

    async def check_single(self, name: str) -> Dict[str, Any]:
        """执行单个健康检查"""
        if name not in self.checks:
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': f'健康检查不存在: {name}',
                'timestamp': datetime.now().isoformat()
            }

        try:
            check_config = self.checks[name]
            check_func = check_config['func']
            timeout = check_config['timeout']
            
            result = await asyncio.wait_for(check_func(), timeout=timeout)
            structured_logger.debug(f"单个健康检查完成: {name}, 状态: {result['status']}")
            return result

        except asyncio.TimeoutError:
            structured_logger.error(f"单个健康检查超时: {name}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': f'健康检查超时 ({timeout}秒)',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            structured_logger.error(f"单个健康检查异常: {name}, 错误: {e}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# 具体健康检查实现
async def database_health_check() -> Dict[str, Any]:
    """数据库健康检查"""
    try:
        start_time = datetime.now()
        
        # 检查数据库连接
        async with DatabaseManager.get_session() as session:
            await session.execute(text("SELECT 1"))
        
        # 获取连接池信息
        connection_info = await DatabaseManager.get_connection_info()
        
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds()

        # 判断健康状态
        if response_time > 1.0:  # 响应时间超过1秒认为是降级
            status = HealthStatus.DEGRADED.value
            message = f'数据库响应较慢: {response_time:.3f}秒'
        else:
            status = HealthStatus.HEALTHY.value
            message = '数据库连接正常'

        return {
            'status': status,
            'message': message,
            'response_time': response_time,
            'connection_info': connection_info,
            'timestamp': end_time.isoformat()
        }
    except Exception as e:
        return {
            'status': HealthStatus.UNHEALTHY.value,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


async def cache_health_check() -> Dict[str, Any]:
    """缓存健康检查"""
    try:
        start_time = datetime.now()
        
        cache_manager = CacheManager.get_instance()
        
        # 测试缓存读写
        test_key = "health_check_test"
        test_value = {"test": True, "timestamp": start_time.isoformat()}
        
        await cache_manager.set(test_key, test_value, 60)
        retrieved_value = await cache_manager.get(test_key)
        await cache_manager.delete(test_key)
        
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds()

        if retrieved_value != test_value:
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': '缓存读写测试失败',
                'timestamp': end_time.isoformat()
            }

        # 判断健康状态
        if response_time > 0.1:  # 响应时间超过100ms认为是降级
            status = HealthStatus.DEGRADED.value
            message = f'缓存响应较慢: {response_time:.3f}秒'
        else:
            status = HealthStatus.HEALTHY.value
            message = '缓存连接正常'

        return {
            'status': status,
            'message': message,
            'response_time': response_time,
            'timestamp': end_time.isoformat()
        }
    except Exception as e:
        return {
            'status': HealthStatus.UNHEALTHY.value,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


async def disk_space_health_check() -> Dict[str, Any]:
    """磁盘空间健康检查"""
    try:
        disk_usage = psutil.disk_usage('/')
        usage_percent = (disk_usage.used / disk_usage.total) * 100

        if usage_percent > 95:
            status = HealthStatus.UNHEALTHY.value
            message = f"磁盘空间严重不足: {usage_percent:.1f}%"
        elif usage_percent > 90:
            status = HealthStatus.DEGRADED.value
            message = f"磁盘空间不足: {usage_percent:.1f}%"
        elif usage_percent > 80:
            status = HealthStatus.DEGRADED.value
            message = f"磁盘空间紧张: {usage_percent:.1f}%"
        else:
            status = HealthStatus.HEALTHY.value
            message = f"磁盘空间正常: {usage_percent:.1f}%"

        return {
            'status': status,
            'message': message,
            'usage_percent': usage_percent,
            'free_bytes': disk_usage.free,
            'total_bytes': disk_usage.total,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'status': HealthStatus.UNHEALTHY.value,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


async def memory_health_check() -> Dict[str, Any]:
    """内存健康检查"""
    try:
        memory = psutil.virtual_memory()
        usage_percent = memory.percent

        if usage_percent > 95:
            status = HealthStatus.UNHEALTHY.value
            message = f"内存使用率过高: {usage_percent:.1f}%"
        elif usage_percent > 85:
            status = HealthStatus.DEGRADED.value
            message = f"内存使用率较高: {usage_percent:.1f}%"
        else:
            status = HealthStatus.HEALTHY.value
            message = f"内存使用正常: {usage_percent:.1f}%"

        return {
            'status': status,
            'message': message,
            'usage_percent': usage_percent,
            'available_bytes': memory.available,
            'total_bytes': memory.total,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'status': HealthStatus.UNHEALTHY.value,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


async def cpu_health_check() -> Dict[str, Any]:
    """CPU健康检查"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0.0, 0.0, 0.0]

        if cpu_percent > 90:
            status = HealthStatus.UNHEALTHY.value
            message = f"CPU使用率过高: {cpu_percent:.1f}%"
        elif cpu_percent > 80:
            status = HealthStatus.DEGRADED.value
            message = f"CPU使用率较高: {cpu_percent:.1f}%"
        else:
            status = HealthStatus.HEALTHY.value
            message = f"CPU使用正常: {cpu_percent:.1f}%"

        return {
            'status': status,
            'message': message,
            'cpu_percent': cpu_percent,
            'cpu_count': cpu_count,
            'load_average': load_avg,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'status': HealthStatus.UNHEALTHY.value,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


# 全局健康检查器实例
health_checker = HealthChecker()

# 注册默认健康检查
health_checker.register_check("database", database_health_check, timeout=5)
health_checker.register_check("cache", cache_health_check, timeout=3)
health_checker.register_check("disk_space", disk_space_health_check, timeout=2)
health_checker.register_check("memory", memory_health_check, timeout=2)
health_checker.register_check("cpu", cpu_health_check, timeout=3)
