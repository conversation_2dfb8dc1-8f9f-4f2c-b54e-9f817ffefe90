"""
用户仓储层模块
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, delete, update
from sqlalchemy.orm import selectinload, joinedload

from core.base.repository import SQLAlchemyRepository
from core.dependencies import repository
from core.database.query_optimizer import QueryBuilder, EagerLoadingService
from ..models.user import SysUserModel, SysUserRoleModel, SysUserPostModel
from ..schemas.user import UserCreateSchema, UserUpdateSchema, UserQuerySchema


@repository()
class UserRepository(SQLAlchemyRepository[SysUserModel, UserCreateSchema, UserUpdateSchema]):
    """用户仓储类"""

    def __init__(self, session: AsyncSession):
        super().__init__(session, SysUserModel)

    async def get_by_username(self, username: str) -> Optional[SysUserModel]:
        """根据用户名获取用户"""
        query = select(SysUserModel).where(
            and_(
                SysUserModel.user_name == username,
                SysUserModel.del_flag == '0'
            )
        )
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_by_email(self, email: str) -> Optional[SysUserModel]:
        """根据邮箱获取用户"""
        query = select(SysUserModel).where(
            and_(
                SysUserModel.email == email,
                SysUserModel.del_flag == '0'
            )
        )
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_by_phone(self, phone: str) -> Optional[SysUserModel]:
        """根据手机号获取用户"""
        query = select(SysUserModel).where(
            and_(
                SysUserModel.phonenumber == phone,
                SysUserModel.del_flag == '0'
            )
        )
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def list_with_dept_info(
        self,
        filters: UserQuerySchema,
        skip: int = 0,
        limit: int = 100,
        data_scope_sql: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取用户列表（包含部门信息）"""
        # 使用查询构建器
        query_builder = QueryBuilder(SysUserModel)
        
        # 基础过滤条件
        base_filters = {'del_flag': '0'}
        
        # 添加查询条件
        if filters.user_name:
            base_filters['user_name'] = {'like': filters.user_name}
        if filters.nick_name:
            base_filters['nick_name'] = {'like': filters.nick_name}
        if filters.email:
            base_filters['email'] = {'like': filters.email}
        if filters.phonenumber:
            base_filters['phonenumber'] = {'like': filters.phonenumber}
        if filters.status:
            base_filters['status'] = filters.status
        if filters.dept_id:
            base_filters['dept_id'] = filters.dept_id

        # 时间范围过滤
        if filters.begin_time and filters.end_time:
            base_filters['create_time'] = {
                'gte': filters.begin_time,
                'lte': filters.end_time
            }

        query = query_builder.filter_by(**base_filters).build()

        # 添加数据权限SQL
        if data_scope_sql and data_scope_sql != "1=1":
            # 这里需要根据实际的数据权限SQL进行处理
            pass

        # 添加排序和分页
        query = query.order_by(SysUserModel.user_id.desc()).offset(skip).limit(limit)

        result = await self.session.execute(query)
        users = result.scalars().all()

        # 转换为字典格式并添加部门信息
        user_list = []
        for user in users:
            user_dict = {
                'user_id': user.user_id,
                'dept_id': user.dept_id,
                'user_name': user.user_name,
                'nick_name': user.nick_name,
                'user_type': user.user_type,
                'email': user.email,
                'phonenumber': user.phonenumber,
                'sex': user.sex,
                'avatar': user.avatar,
                'status': user.status,
                'login_ip': user.login_ip,
                'login_date': user.login_date,
                'create_by': user.create_by,
                'create_time': user.create_time,
                'update_by': user.update_by,
                'update_time': user.update_time,
                'remark': user.remark,
                'admin': user.user_id == 1,
                'dept_name': None,  # 需要关联查询部门表
                'role_names': None  # 需要关联查询角色表
            }
            user_list.append(user_dict)

        return user_list

    async def count_with_filters(
        self,
        filters: UserQuerySchema,
        data_scope_sql: Optional[str] = None
    ) -> int:
        """统计用户数量（带过滤条件）"""
        query = select(func.count(SysUserModel.user_id)).where(SysUserModel.del_flag == '0')

        # 添加查询条件
        conditions = []
        if filters.user_name:
            conditions.append(SysUserModel.user_name.like(f"%{filters.user_name}%"))
        if filters.nick_name:
            conditions.append(SysUserModel.nick_name.like(f"%{filters.nick_name}%"))
        if filters.email:
            conditions.append(SysUserModel.email.like(f"%{filters.email}%"))
        if filters.phonenumber:
            conditions.append(SysUserModel.phonenumber.like(f"%{filters.phonenumber}%"))
        if filters.status:
            conditions.append(SysUserModel.status == filters.status)
        if filters.dept_id:
            conditions.append(SysUserModel.dept_id == filters.dept_id)

        if conditions:
            query = query.where(and_(*conditions))

        # 添加数据权限SQL
        if data_scope_sql and data_scope_sql != "1=1":
            # 这里需要根据实际的数据权限SQL进行处理
            pass

        result = await self.session.execute(query)
        return result.scalar()

    async def check_username_unique(self, username: str, user_id: Optional[int] = None) -> bool:
        """检查用户名是否唯一"""
        query = select(SysUserModel).where(
            and_(
                SysUserModel.user_name == username,
                SysUserModel.del_flag == '0'
            )
        )
        
        if user_id:
            query = query.where(SysUserModel.user_id != user_id)

        result = await self.session.execute(query)
        return result.scalar_one_or_none() is None

    async def check_email_unique(self, email: str, user_id: Optional[int] = None) -> bool:
        """检查邮箱是否唯一"""
        if not email:
            return True

        query = select(SysUserModel).where(
            and_(
                SysUserModel.email == email,
                SysUserModel.del_flag == '0'
            )
        )
        
        if user_id:
            query = query.where(SysUserModel.user_id != user_id)

        result = await self.session.execute(query)
        return result.scalar_one_or_none() is None

    async def check_phone_unique(self, phone: str, user_id: Optional[int] = None) -> bool:
        """检查手机号是否唯一"""
        if not phone:
            return True

        query = select(SysUserModel).where(
            and_(
                SysUserModel.phonenumber == phone,
                SysUserModel.del_flag == '0'
            )
        )
        
        if user_id:
            query = query.where(SysUserModel.user_id != user_id)

        result = await self.session.execute(query)
        return result.scalar_one_or_none() is None

    async def soft_delete(self, user_id: int) -> bool:
        """软删除用户"""
        query = update(SysUserModel).where(SysUserModel.user_id == user_id).values(del_flag='2')
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount > 0

    async def batch_soft_delete(self, user_ids: List[int]) -> int:
        """批量软删除用户"""
        query = update(SysUserModel).where(SysUserModel.user_id.in_(user_ids)).values(del_flag='2')
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount

    async def update_login_info(self, user_id: int, login_ip: str) -> bool:
        """更新登录信息"""
        from datetime import datetime
        query = update(SysUserModel).where(SysUserModel.user_id == user_id).values(
            login_ip=login_ip,
            login_date=datetime.now()
        )
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount > 0


@repository()
class UserRoleRepository:
    """用户角色关联仓储类"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_user_roles(self, user_id: int) -> List[int]:
        """获取用户角色ID列表"""
        query = select(SysUserRoleModel.role_id).where(SysUserRoleModel.user_id == user_id)
        result = await self.session.execute(query)
        return [row[0] for row in result.fetchall()]

    async def delete_user_roles(self, user_id: int) -> int:
        """删除用户的所有角色关联"""
        query = delete(SysUserRoleModel).where(SysUserRoleModel.user_id == user_id)
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount

    async def batch_insert_user_roles(self, user_id: int, role_ids: List[int]) -> int:
        """批量插入用户角色关联"""
        if not role_ids:
            return 0

        user_roles = [{'user_id': user_id, 'role_id': role_id} for role_id in role_ids]
        
        await self.session.execute(
            SysUserRoleModel.__table__.insert(),
            user_roles
        )
        await self.session.flush()
        return len(user_roles)


@repository()
class UserPostRepository:
    """用户岗位关联仓储类"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_user_posts(self, user_id: int) -> List[int]:
        """获取用户岗位ID列表"""
        query = select(SysUserPostModel.post_id).where(SysUserPostModel.user_id == user_id)
        result = await self.session.execute(query)
        return [row[0] for row in result.fetchall()]

    async def delete_user_posts(self, user_id: int) -> int:
        """删除用户的所有岗位关联"""
        query = delete(SysUserPostModel).where(SysUserPostModel.user_id == user_id)
        result = await self.session.execute(query)
        await self.session.flush()
        return result.rowcount

    async def batch_insert_user_posts(self, user_id: int, post_ids: List[int]) -> int:
        """批量插入用户岗位关联"""
        if not post_ids:
            return 0

        user_posts = [{'user_id': user_id, 'post_id': post_id} for post_id in post_ids]
        
        await self.session.execute(
            SysUserPostModel.__table__.insert(),
            user_posts
        )
        await self.session.flush()
        return len(user_posts)
