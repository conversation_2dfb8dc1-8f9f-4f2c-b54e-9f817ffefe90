"""
标准化异常类模块
"""
from typing import Any, Optional, Dict
from datetime import datetime
import traceback
import uuid

from .error_codes import ErrorCode


class StandardException(Exception):
    """标准化异常基类"""

    def __init__(
        self, 
        error_code: ErrorCode, 
        detail: Optional[str] = None, 
        data: Optional[Any] = None,
        cause: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.error_code = error_code
        self.detail = detail or error_code.message
        self.data = data
        self.cause = cause
        self.context = context or {}
        self.timestamp = datetime.now()
        self.trace_id = str(uuid.uuid4())
        
        # 设置异常链
        if cause:
            self.__cause__ = cause
        
        super().__init__(self.detail)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'error_code': self.error_code.code,
            'error_name': self.error_code.name,
            'message': self.detail,
            'timestamp': self.timestamp.isoformat(),
            'trace_id': self.trace_id
        }
        
        if self.data is not None:
            result['data'] = self.data
            
        if self.context:
            result['context'] = self.context
            
        if self.cause:
            result['cause'] = str(self.cause)
            
        return result

    def __str__(self):
        return f"[{self.error_code.code}] {self.detail}"

    def __repr__(self):
        return f"{self.__class__.__name__}({self.error_code}, '{self.detail}')"


class BusinessException(StandardException):
    """业务异常"""
    
    def __init__(
        self, 
        error_code: ErrorCode, 
        detail: Optional[str] = None, 
        data: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(error_code, detail, data, **kwargs)


class SystemException(StandardException):
    """系统异常"""
    
    def __init__(
        self, 
        error_code: ErrorCode, 
        detail: Optional[str] = None, 
        data: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(error_code, detail, data, **kwargs)


class ValidationException(StandardException):
    """验证异常"""
    
    def __init__(
        self, 
        detail: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs
    ):
        data = {}
        if field:
            data['field'] = field
        if value is not None:
            data['value'] = value
            
        super().__init__(ErrorCode.VALIDATION_ERROR, detail, data, **kwargs)


class AuthenticationException(StandardException):
    """认证异常"""
    
    def __init__(
        self, 
        error_code: ErrorCode = ErrorCode.UNAUTHORIZED,
        detail: Optional[str] = None, 
        **kwargs
    ):
        super().__init__(error_code, detail, **kwargs)


class AuthorizationException(StandardException):
    """授权异常"""
    
    def __init__(
        self, 
        detail: Optional[str] = None,
        required_permission: Optional[str] = None,
        **kwargs
    ):
        data = {}
        if required_permission:
            data['required_permission'] = required_permission
            
        super().__init__(ErrorCode.PERMISSION_DENIED, detail, data, **kwargs)


class ResourceNotFoundException(StandardException):
    """资源不存在异常"""
    
    def __init__(
        self, 
        resource_type: str,
        resource_id: Optional[Any] = None,
        detail: Optional[str] = None,
        **kwargs
    ):
        if not detail:
            detail = f"{resource_type}不存在"
            if resource_id:
                detail += f": {resource_id}"
                
        data = {'resource_type': resource_type}
        if resource_id:
            data['resource_id'] = resource_id
            
        super().__init__(ErrorCode.RESOURCE_NOT_FOUND, detail, data, **kwargs)


class ResourceAlreadyExistsException(StandardException):
    """资源已存在异常"""
    
    def __init__(
        self, 
        resource_type: str,
        resource_id: Optional[Any] = None,
        detail: Optional[str] = None,
        **kwargs
    ):
        if not detail:
            detail = f"{resource_type}已存在"
            if resource_id:
                detail += f": {resource_id}"
                
        data = {'resource_type': resource_type}
        if resource_id:
            data['resource_id'] = resource_id
            
        super().__init__(ErrorCode.RESOURCE_ALREADY_EXISTS, detail, data, **kwargs)


class DatabaseException(SystemException):
    """数据库异常"""
    
    def __init__(
        self, 
        detail: Optional[str] = None,
        operation: Optional[str] = None,
        table: Optional[str] = None,
        **kwargs
    ):
        data = {}
        if operation:
            data['operation'] = operation
        if table:
            data['table'] = table
            
        super().__init__(ErrorCode.DATABASE_ERROR, detail, data, **kwargs)


class CacheException(SystemException):
    """缓存异常"""
    
    def __init__(
        self, 
        detail: Optional[str] = None,
        operation: Optional[str] = None,
        key: Optional[str] = None,
        **kwargs
    ):
        data = {}
        if operation:
            data['operation'] = operation
        if key:
            data['key'] = key
            
        super().__init__(ErrorCode.CACHE_ERROR, detail, data, **kwargs)


class ExternalServiceException(StandardException):
    """外部服务异常"""
    
    def __init__(
        self, 
        service_name: str,
        detail: Optional[str] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Any] = None,
        **kwargs
    ):
        if not detail:
            detail = f"外部服务调用失败: {service_name}"
            
        data = {'service_name': service_name}
        if status_code:
            data['status_code'] = status_code
        if response_data:
            data['response_data'] = response_data
            
        super().__init__(ErrorCode.EXTERNAL_SERVICE_ERROR, detail, data, **kwargs)


class RateLimitException(StandardException):
    """限流异常"""
    
    def __init__(
        self, 
        limit: int,
        window: int,
        detail: Optional[str] = None,
        **kwargs
    ):
        if not detail:
            detail = f"请求频率超限: {limit}次/{window}秒"
            
        data = {
            'limit': limit,
            'window': window
        }
        
        super().__init__(ErrorCode.RATE_LIMIT_EXCEEDED, detail, data, **kwargs)


# 异常工厂类
class ExceptionFactory:
    """异常工厂类"""
    
    @staticmethod
    def create_business_exception(
        error_code: ErrorCode, 
        detail: Optional[str] = None,
        **kwargs
    ) -> BusinessException:
        """创建业务异常"""
        return BusinessException(error_code, detail, **kwargs)
    
    @staticmethod
    def create_validation_exception(
        detail: str,
        field: Optional[str] = None,
        **kwargs
    ) -> ValidationException:
        """创建验证异常"""
        return ValidationException(detail, field, **kwargs)
    
    @staticmethod
    def create_not_found_exception(
        resource_type: str,
        resource_id: Optional[Any] = None,
        **kwargs
    ) -> ResourceNotFoundException:
        """创建资源不存在异常"""
        return ResourceNotFoundException(resource_type, resource_id, **kwargs)
    
    @staticmethod
    def create_unauthorized_exception(
        detail: Optional[str] = None,
        **kwargs
    ) -> AuthenticationException:
        """创建未认证异常"""
        return AuthenticationException(ErrorCode.UNAUTHORIZED, detail, **kwargs)
    
    @staticmethod
    def create_forbidden_exception(
        detail: Optional[str] = None,
        required_permission: Optional[str] = None,
        **kwargs
    ) -> AuthorizationException:
        """创建权限不足异常"""
        return AuthorizationException(detail, required_permission, **kwargs)
