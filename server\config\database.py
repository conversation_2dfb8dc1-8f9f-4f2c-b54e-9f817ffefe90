"""
数据库配置模块
"""
from pydantic import computed_field
from pydantic_settings import BaseSettings
from typing import Literal


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    type: Literal['mysql', 'postgresql'] = 'mysql'
    host: str = '127.0.0.1'
    port: int = 3306
    username: str = 'root'
    password: str = '1234'
    database: str = 'ruoyi-fastapi'
    echo: bool = True
    pool_size: int = 50
    max_overflow: int = 10
    pool_recycle: int = 3600
    pool_timeout: int = 30
    
    @computed_field
    @property
    def sqlglot_parse_dialect(self) -> str:
        """SQLGlot解析方言"""
        if self.type == 'postgresql':
            return 'postgres'
        return self.type
    
    class Config:
        env_prefix = 'DB_'
        env_file = '.env'
