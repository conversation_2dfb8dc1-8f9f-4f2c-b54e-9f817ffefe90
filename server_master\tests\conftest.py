"""
测试配置文件
"""
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from core.application import ApplicationFactory
from core.database.base import Base
from core.dependencies import get_db
from config import settings


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """测试数据库引擎"""
    # 使用内存SQLite数据库进行测试
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False,
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        }
    )
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # 清理
    await engine.dispose()


@pytest.fixture(scope="session")
async def test_session_factory(test_engine):
    """测试会话工厂"""
    async_session_factory = sessionmaker(
        test_engine, 
        class_=AsyncSession, 
        expire_on_commit=False
    )
    return async_session_factory


@pytest.fixture
async def db_session(test_session_factory):
    """数据库会话"""
    async with test_session_factory() as session:
        yield session
        await session.rollback()


@pytest.fixture
async def test_app(db_session):
    """测试应用"""
    app = ApplicationFactory.create_app()
    
    # 覆盖数据库依赖
    app.dependency_overrides[get_db] = lambda: db_session
    
    return app


@pytest.fixture
async def client(test_app):
    """测试客户端"""
    async with AsyncClient(app=test_app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def authenticated_client(client, db_session):
    """认证客户端"""
    # 创建测试用户
    from apps.admin.models.user import SysUserModel
    from apps.admin.services.auth import AuthService
    
    test_user = SysUserModel(
        user_name="test_user",
        nick_name="测试用户",
        email="<EMAIL>",
        password="hashed_password",
        salt="test_salt",
        status="0"
    )
    
    db_session.add(test_user)
    await db_session.commit()
    await db_session.refresh(test_user)
    
    # 生成JWT令牌
    auth_service = AuthService(db_session, None, None)  # 简化初始化
    token = await auth_service.jwt_manager.create_access_token({
        'user_id': test_user.user_id,
        'username': test_user.user_name
    })
    
    # 设置认证头
    client.headers.update({"Authorization": f"Bearer {token}"})
    
    yield client


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "user_name": "test_user",
        "nick_name": "测试用户",
        "email": "<EMAIL>",
        "phonenumber": "13800138000",
        "sex": "1",
        "password": "test123456",
        "status": "0"
    }


@pytest.fixture
def sample_role_data():
    """示例角色数据"""
    return {
        "role_name": "测试角色",
        "role_key": "test_role",
        "role_sort": 1,
        "status": "0",
        "remark": "测试角色"
    }


@pytest.fixture
def sample_menu_data():
    """示例菜单数据"""
    return {
        "menu_name": "测试菜单",
        "parent_id": 0,
        "order_num": 1,
        "path": "/test",
        "component": "test/index",
        "menu_type": "C",
        "visible": "0",
        "status": "0",
        "perms": "test:list",
        "icon": "test"
    }


@pytest.fixture
def sample_dept_data():
    """示例部门数据"""
    return {
        "dept_name": "测试部门",
        "parent_id": 0,
        "order_num": 1,
        "leader": "测试负责人",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "status": "0"
    }


class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    async def create_user(db_session: AsyncSession, **kwargs):
        """创建测试用户"""
        from apps.admin.models.user import SysUserModel
        
        default_data = {
            "user_name": "test_user",
            "nick_name": "测试用户",
            "email": "<EMAIL>",
            "password": "hashed_password",
            "salt": "test_salt",
            "status": "0"
        }
        default_data.update(kwargs)
        
        user = SysUserModel(**default_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @staticmethod
    async def create_role(db_session: AsyncSession, **kwargs):
        """创建测试角色"""
        from apps.admin.models.role import SysRoleModel
        
        default_data = {
            "role_name": "测试角色",
            "role_key": "test_role",
            "role_sort": 1,
            "status": "0"
        }
        default_data.update(kwargs)
        
        role = SysRoleModel(**default_data)
        db_session.add(role)
        await db_session.commit()
        await db_session.refresh(role)
        return role
    
    @staticmethod
    async def create_menu(db_session: AsyncSession, **kwargs):
        """创建测试菜单"""
        from apps.admin.models.menu import SysMenuModel
        
        default_data = {
            "menu_name": "测试菜单",
            "parent_id": 0,
            "order_num": 1,
            "path": "/test",
            "menu_type": "C",
            "visible": "0",
            "status": "0"
        }
        default_data.update(kwargs)
        
        menu = SysMenuModel(**default_data)
        db_session.add(menu)
        await db_session.commit()
        await db_session.refresh(menu)
        return menu
    
    @staticmethod
    async def create_dept(db_session: AsyncSession, **kwargs):
        """创建测试部门"""
        from apps.admin.models.dept import SysDeptModel
        
        default_data = {
            "dept_name": "测试部门",
            "parent_id": 0,
            "order_num": 1,
            "status": "0"
        }
        default_data.update(kwargs)
        
        dept = SysDeptModel(**default_data)
        db_session.add(dept)
        await db_session.commit()
        await db_session.refresh(dept)
        return dept


@pytest.fixture
def test_data_factory():
    """测试数据工厂fixture"""
    return TestDataFactory
