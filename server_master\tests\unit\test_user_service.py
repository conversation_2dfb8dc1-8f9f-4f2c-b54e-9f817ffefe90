"""
用户服务单元测试
"""
import pytest
from unittest.mock import Mock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from apps.admin.services.user import UserService
from apps.admin.schemas.user import UserCreateSchema, UserUpdateSchema
from apps.admin.models.user import SysUserModel
from core.exceptions import BusinessException, ErrorCode


class TestUserService:
    """用户服务测试类"""

    @pytest.fixture
    async def user_service(self, db_session):
        """用户服务实例"""
        return UserService(db_session)

    @pytest.fixture
    def user_create_data(self):
        """用户创建数据"""
        return UserCreateSchema(
            user_name="test_user",
            nick_name="测试用户",
            email="<EMAIL>",
            phonenumber="***********",
            password="test123456"
        )

    @pytest.fixture
    def user_update_data(self):
        """用户更新数据"""
        return UserUpdateSchema(
            nick_name="更新的用户",
            email="<EMAIL>",
            phonenumber="***********"
        )

    async def test_create_user_success(self, user_service, user_create_data, db_session):
        """测试创建用户成功"""
        # 执行创建
        result = await user_service.create_user(user_create_data, "admin")
        
        # 验证结果
        assert result is not None
        assert result.user_name == "test_user"
        assert result.nick_name == "测试用户"
        assert result.email == "<EMAIL>"
        assert result.status == "0"
        
        # 验证数据库中的记录
        from sqlalchemy import select
        stmt = select(SysUserModel).where(SysUserModel.user_name == "test_user")
        db_result = await db_session.execute(stmt)
        user = db_result.scalar_one_or_none()
        
        assert user is not None
        assert user.user_name == "test_user"

    async def test_create_user_duplicate_username(self, user_service, user_create_data, test_data_factory, db_session):
        """测试创建用户时用户名重复"""
        # 先创建一个用户
        await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 尝试创建同名用户
        with pytest.raises(BusinessException) as exc_info:
            await user_service.create_user(user_create_data, "admin")
        
        assert exc_info.value.error_code == ErrorCode.RESOURCE_ALREADY_EXISTS

    async def test_get_user_by_id_success(self, user_service, test_data_factory, db_session):
        """测试根据ID获取用户成功"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 获取用户
        result = await user_service.get_user_detail(user.user_id)
        
        # 验证结果
        assert result is not None
        assert result.user_id == user.user_id
        assert result.user_name == "test_user"

    async def test_get_user_by_id_not_found(self, user_service):
        """测试根据ID获取用户不存在"""
        result = await user_service.get_user_detail(99999)
        assert result is None

    async def test_update_user_success(self, user_service, user_update_data, test_data_factory, db_session):
        """测试更新用户成功"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 更新用户
        user_update_data.user_id = user.user_id
        result = await user_service.update_user(user_update_data, "admin")
        
        # 验证结果
        assert result is not None
        assert result.nick_name == "更新的用户"
        assert result.email == "<EMAIL>"

    async def test_update_user_not_found(self, user_service, user_update_data):
        """测试更新不存在的用户"""
        user_update_data.user_id = 99999
        
        with pytest.raises(BusinessException) as exc_info:
            await user_service.update_user(user_update_data, "admin")
        
        assert exc_info.value.error_code == ErrorCode.RESOURCE_NOT_FOUND

    async def test_delete_user_success(self, user_service, test_data_factory, db_session):
        """测试删除用户成功"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 删除用户
        result = await user_service.delete_user(user.user_id, "admin")
        
        # 验证结果
        assert result is True
        
        # 验证用户已被软删除
        from sqlalchemy import select
        stmt = select(SysUserModel).where(SysUserModel.user_id == user.user_id)
        db_result = await db_session.execute(stmt)
        deleted_user = db_result.scalar_one_or_none()
        
        assert deleted_user.del_flag == "2"

    async def test_delete_admin_user_forbidden(self, user_service, test_data_factory, db_session):
        """测试删除管理员用户被禁止"""
        # 创建管理员用户
        admin_user = await test_data_factory.create_user(
            db_session, 
            user_name="admin", 
            user_id=1
        )
        
        # 尝试删除管理员
        with pytest.raises(BusinessException) as exc_info:
            await user_service.delete_user(admin_user.user_id, "admin")
        
        assert exc_info.value.error_code == ErrorCode.OPERATION_NOT_ALLOWED

    async def test_batch_delete_users_success(self, user_service, test_data_factory, db_session):
        """测试批量删除用户成功"""
        # 创建多个测试用户
        user1 = await test_data_factory.create_user(db_session, user_name="test_user1")
        user2 = await test_data_factory.create_user(db_session, user_name="test_user2")
        
        # 批量删除
        user_ids = [user1.user_id, user2.user_id]
        result = await user_service.batch_delete_users(user_ids, "admin")
        
        # 验证结果
        assert result == 2

    async def test_get_user_list_with_filters(self, user_service, test_data_factory, db_session):
        """测试带过滤条件的用户列表查询"""
        # 创建测试用户
        await test_data_factory.create_user(db_session, user_name="test_user1", nick_name="测试用户1")
        await test_data_factory.create_user(db_session, user_name="test_user2", nick_name="测试用户2")
        await test_data_factory.create_user(db_session, user_name="other_user", nick_name="其他用户")
        
        # 查询用户列表
        from apps.admin.schemas.user import UserQuerySchema
        query = UserQuerySchema(user_name="test")
        
        result = await user_service.get_user_list(query, 1, 10)
        
        # 验证结果
        assert result['total'] == 2
        assert len(result['rows']) == 2

    async def test_check_username_unique(self, user_service, test_data_factory, db_session):
        """测试检查用户名唯一性"""
        # 创建测试用户
        await test_data_factory.create_user(db_session, user_name="existing_user")
        
        # 检查已存在的用户名
        is_unique = await user_service.user_repo.check_username_unique("existing_user")
        assert is_unique is False
        
        # 检查不存在的用户名
        is_unique = await user_service.user_repo.check_username_unique("new_user")
        assert is_unique is True

    async def test_update_login_info(self, user_service, test_data_factory, db_session):
        """测试更新登录信息"""
        # 创建测试用户
        user = await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 更新登录信息
        await user_service.update_login_info(user.user_id, "***********")
        
        # 验证更新
        updated_user = await user_service.get_user_detail(user.user_id)
        assert updated_user.login_ip == "***********"
        assert updated_user.login_date is not None

    async def test_get_by_username(self, user_service, test_data_factory, db_session):
        """测试根据用户名获取用户"""
        # 创建测试用户
        await test_data_factory.create_user(db_session, user_name="test_user")
        
        # 根据用户名获取用户
        result = await user_service.get_by_username("test_user")
        
        # 验证结果
        assert result is not None
        assert result.user_name == "test_user"

    async def test_get_by_username_not_found(self, user_service):
        """测试根据用户名获取不存在的用户"""
        result = await user_service.get_by_username("nonexistent_user")
        assert result is None
