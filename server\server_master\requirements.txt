# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库相关
sqlalchemy==2.0.23
asyncmy==0.2.8
aiomysql==0.2.0
asyncpg==0.29.0
aiosqlite==0.19.0
alembic==1.13.0

# Redis缓存
aioredis==2.0.1

# JWT认证
PyJWT==2.8.0
cryptography==41.0.8

# 密码加密
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 日志
loguru==0.7.2

# 配置管理
python-dotenv==1.0.0

# 系统监控
psutil==5.9.6

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 时间处理
python-dateutil==2.8.2

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9
Pillow==10.1.0

# 模板引擎
Jinja2==3.1.2

# 任务调度
APScheduler==3.10.4

# 数据验证
email-validator==2.1.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
