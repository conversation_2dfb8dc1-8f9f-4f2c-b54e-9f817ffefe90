"""
通用工具类
"""
import re
import os
import pandas as pd
from typing import Any, Dict, List, Literal, Union, Optional
from sqlalchemy.engine.row import Row
from sqlalchemy.orm.collections import InstrumentedList
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation

from core.database.base import Base


class CamelCaseUtil:
    """驼峰命名工具类"""

    @staticmethod
    def snake_to_camel(snake_str: str, first_upper: bool = False) -> str:
        """
        下划线转驼峰命名
        
        Args:
            snake_str: 下划线命名字符串
            first_upper: 首字母是否大写
            
        Returns:
            驼峰命名字符串
        """
        if not snake_str:
            return snake_str
            
        components = snake_str.split('_')
        if first_upper:
            return ''.join(word.capitalize() for word in components)
        else:
            return components[0] + ''.join(word.capitalize() for word in components[1:])

    @staticmethod
    def camel_to_snake(camel_str: str) -> str:
        """
        驼峰命名转下划线
        
        Args:
            camel_str: 驼峰命名字符串
            
        Returns:
            下划线命名字符串
        """
        if not camel_str:
            return camel_str
            
        # 在大写字母前插入下划线
        snake_str = re.sub('([a-z0-9])([A-Z])', r'\1_\2', camel_str)
        return snake_str.lower()

    @staticmethod
    def transform_keys(data: Union[Dict, List], to_camel: bool = True) -> Union[Dict, List]:
        """
        递归转换字典或列表中的键名
        
        Args:
            data: 要转换的数据
            to_camel: True转为驼峰，False转为下划线
            
        Returns:
            转换后的数据
        """
        if isinstance(data, dict):
            new_dict = {}
            for key, value in data.items():
                if isinstance(key, str):
                    new_key = CamelCaseUtil.snake_to_camel(key) if to_camel else CamelCaseUtil.camel_to_snake(key)
                else:
                    new_key = key
                new_dict[new_key] = CamelCaseUtil.transform_keys(value, to_camel)
            return new_dict
        elif isinstance(data, list):
            return [CamelCaseUtil.transform_keys(item, to_camel) for item in data]
        else:
            return data


class SnakeCaseUtil:
    """下划线命名工具类"""

    @staticmethod
    def camel_to_snake(camel_str: str) -> str:
        """驼峰转下划线"""
        return CamelCaseUtil.camel_to_snake(camel_str)

    @staticmethod
    def snake_to_camel(snake_str: str, first_upper: bool = False) -> str:
        """下划线转驼峰"""
        return CamelCaseUtil.snake_to_camel(snake_str, first_upper)


class SqlalchemyUtil:
    """SQLAlchemy工具类"""

    @classmethod
    def model_to_dict(
        cls, 
        obj: Union[Base, Dict],
        transform_case: Literal['no_case', 'snake_to_camel', 'camel_to_snake'] = 'no_case',
        exclude_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        将SQLAlchemy模型对象转换为字典
        
        Args:
            obj: 模型对象或字典
            transform_case: 字段名转换方式
            exclude_fields: 要排除的字段列表
            
        Returns:
            转换后的字典
        """
        if obj is None:
            return {}
            
        exclude_fields = exclude_fields or []
        
        if isinstance(obj, dict):
            result = obj.copy()
        elif isinstance(obj, Row):
            result = dict(obj._mapping)
        elif hasattr(obj, '__dict__'):
            result = {}
            for key, value in obj.__dict__.items():
                if not key.startswith('_') and key not in exclude_fields:
                    if isinstance(value, InstrumentedList):
                        # 处理关联对象列表
                        result[key] = [cls.model_to_dict(item, transform_case, exclude_fields) for item in value]
                    elif hasattr(value, '__dict__') and not isinstance(value, (str, int, float, bool, type(None))):
                        # 处理关联对象
                        result[key] = cls.model_to_dict(value, transform_case, exclude_fields)
                    else:
                        result[key] = value
        else:
            return obj
        
        # 转换字段名
        if transform_case == 'snake_to_camel':
            result = CamelCaseUtil.transform_keys(result, to_camel=True)
        elif transform_case == 'camel_to_snake':
            result = CamelCaseUtil.transform_keys(result, to_camel=False)
        
        return result

    @classmethod
    def serialize_result(
        cls,
        result: Union[List, Dict, Base, Row, None],
        transform_case: Literal['no_case', 'snake_to_camel', 'camel_to_snake'] = 'no_case',
        exclude_fields: Optional[List[str]] = None
    ) -> Union[List[Dict], Dict, None]:
        """
        序列化查询结果
        
        Args:
            result: 查询结果
            transform_case: 字段名转换方式
            exclude_fields: 要排除的字段列表
            
        Returns:
            序列化后的结果
        """
        if result is None:
            return None
            
        if isinstance(result, list):
            return [cls.model_to_dict(item, transform_case, exclude_fields) for item in result]
        else:
            return cls.model_to_dict(result, transform_case, exclude_fields)


class ExcelUtil:
    """Excel工具类"""

    @staticmethod
    def export_to_excel(
        data: List[Dict[str, Any]], 
        headers: Dict[str, str],
        filename: str = 'export.xlsx',
        sheet_name: str = 'Sheet1'
    ) -> str:
        """
        导出数据到Excel文件
        
        Args:
            data: 要导出的数据列表
            headers: 列头映射 {字段名: 显示名}
            filename: 文件名
            sheet_name: 工作表名
            
        Returns:
            文件路径
        """
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_name
        
        # 设置表头
        header_row = 1
        for col_num, (field, display_name) in enumerate(headers.items(), 1):
            cell = ws.cell(row=header_row, column=col_num, value=display_name)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 填充数据
        for row_num, item in enumerate(data, 2):
            for col_num, field in enumerate(headers.keys(), 1):
                value = item.get(field, '')
                ws.cell(row=row_num, column=col_num, value=value)
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        file_path = os.path.join('storage', 'exports', filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        wb.save(file_path)
        
        return file_path

    @staticmethod
    def import_from_excel(
        file_path: str,
        headers: Dict[str, str],
        sheet_name: Optional[str] = None,
        start_row: int = 2
    ) -> List[Dict[str, Any]]:
        """
        从Excel文件导入数据
        
        Args:
            file_path: Excel文件路径
            headers: 列头映射 {显示名: 字段名}
            sheet_name: 工作表名，None表示第一个工作表
            start_row: 数据开始行号
            
        Returns:
            导入的数据列表
        """
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)
        
        # 重命名列
        df.rename(columns=headers, inplace=True)
        
        # 从指定行开始读取数据
        if start_row > 1:
            df = df.iloc[start_row-1:]
        
        # 转换为字典列表
        data = df.to_dict('records')
        
        # 清理空值
        cleaned_data = []
        for item in data:
            cleaned_item = {k: v for k, v in item.items() if pd.notna(v)}
            if cleaned_item:  # 跳过空行
                cleaned_data.append(cleaned_item)
        
        return cleaned_data


class FileUtil:
    """文件工具类"""

    @staticmethod
    def get_file_size_human(file_path: str) -> str:
        """
        获取人类可读的文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            格式化的文件大小
        """
        try:
            size = os.path.getsize(file_path)
            return FileUtil.bytes_to_human(size)
        except OSError:
            return "0B"

    @staticmethod
    def bytes_to_human(size_bytes: int, format_str: str = '%(value).1f%(symbol)s') -> str:
        """
        字节数转换为人类可读格式
        
        Args:
            size_bytes: 字节数
            format_str: 格式化字符串
            
        Returns:
            格式化的大小字符串
        """
        symbols = ('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB')
        prefix = {}
        
        for i, symbol in enumerate(symbols[1:]):
            prefix[symbol] = 1 << (i + 1) * 10
        
        # 查找合适的单位
        symbol = next((s for s in reversed(symbols[1:]) if size_bytes >= prefix[s]), None)
        
        if symbol:
            value = float(size_bytes) / prefix[symbol]
            return format_str % {'value': value, 'symbol': symbol}
        
        return format_str % {'value': size_bytes, 'symbol': symbols[0]}

    @staticmethod
    def ensure_dir(dir_path: str) -> str:
        """
        确保目录存在，不存在则创建
        
        Args:
            dir_path: 目录路径
            
        Returns:
            目录路径
        """
        os.makedirs(dir_path, exist_ok=True)
        return dir_path

    @staticmethod
    def get_file_extension(filename: str) -> str:
        """
        获取文件扩展名
        
        Args:
            filename: 文件名
            
        Returns:
            文件扩展名（不包含点号）
        """
        return os.path.splitext(filename)[1][1:].lower()

    @staticmethod
    def is_allowed_file(filename: str, allowed_extensions: List[str]) -> bool:
        """
        检查文件扩展名是否允许
        
        Args:
            filename: 文件名
            allowed_extensions: 允许的扩展名列表
            
        Returns:
            是否允许
        """
        extension = FileUtil.get_file_extension(filename)
        return extension in [ext.lower() for ext in allowed_extensions]


class ValidationUtil:
    """验证工具类"""

    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """验证手机号格式"""
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))

    @staticmethod
    def is_valid_id_card(id_card: str) -> bool:
        """验证身份证号格式"""
        pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
        return bool(re.match(pattern, id_card))

    @staticmethod
    def clean_html(text: str) -> str:
        """清理HTML标签"""
        import html
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', text)
        # 解码HTML实体
        clean_text = html.unescape(clean_text)
        return clean_text.strip()

    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            验证结果字典
        """
        checks = {
            'length': len(password) >= 8,
            'uppercase': bool(re.search(r'[A-Z]', password)),
            'lowercase': bool(re.search(r'[a-z]', password)),
            'digit': bool(re.search(r'\d', password)),
            'special': bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password)),
        }
        
        score = sum(checks.values())
        
        if score < 3:
            strength = 'weak'
        elif score < 5:
            strength = 'medium'
        else:
            strength = 'strong'
        
        return {
            'score': score,
            'strength': strength,
            'checks': checks,
            'valid': score >= 3
        }
