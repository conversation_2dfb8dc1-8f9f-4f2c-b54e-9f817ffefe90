"""
结构化日志器模块
"""
import json
import sys
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps
from contextvars import ContextVar
from loguru import logger

# 请求上下文变量
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
user_id_var: ContextVar[str] = ContextVar('user_id', default='')
trace_id_var: ContextVar[str] = ContextVar('trace_id', default='')


class StructuredLogger:
    """结构化日志器"""

    def __init__(self):
        self.logger = logger
        self._setup_logger()

    def _setup_logger(self):
        """配置日志器"""
        # 移除默认处理器
        self.logger.remove()

        # 添加控制台处理器
        self.logger.add(
            sink=sys.stdout,
            format=self._format_console_log,
            level="INFO",
            colorize=True,
            backtrace=True,
            diagnose=True
        )

        # 添加文件处理器 - 应用日志
        self.logger.add(
            sink="logs/app_{time:YYYY-MM-DD}.log",
            format=self._format_file_log,
            level="DEBUG",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )

        # 添加错误文件处理器
        self.logger.add(
            sink="logs/error_{time:YYYY-MM-DD}.log",
            format=self._format_file_log,
            level="ERROR",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )

        # 添加访问日志处理器
        self.logger.add(
            sink="logs/access_{time:YYYY-MM-DD}.log",
            format=self._format_access_log,
            level="INFO",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: record["extra"].get("log_type") == "access"
        )

    def _format_console_log(self, record):
        """格式化控制台日志"""
        # 简化的控制台输出格式
        time_str = record["time"].strftime("%Y-%m-%d %H:%M:%S")
        level = record["level"].name
        message = record["message"]
        module = record["name"]
        
        request_id = request_id_var.get('')
        if request_id:
            return f"<green>{time_str}</green> | <level>{level:8}</level> | <cyan>{module}</cyan> | <yellow>{request_id}</yellow> | {message}\n"
        else:
            return f"<green>{time_str}</green> | <level>{level:8}</level> | <cyan>{module}</cyan> | {message}\n"

    def _format_file_log(self, record):
        """格式化文件日志"""
        log_data = {
            'timestamp': record["time"].isoformat(),
            'level': record["level"].name,
            'message': record["message"],
            'module': record["name"],
            'function': record["function"],
            'line': record["line"],
            'request_id': request_id_var.get(''),
            'user_id': user_id_var.get(''),
            'trace_id': trace_id_var.get(''),
        }

        # 添加额外字段
        if record["extra"]:
            log_data.update(record["extra"])

        # 添加异常信息
        if record["exception"]:
            log_data['exception'] = {
                'type': record["exception"].type.__name__,
                'message': str(record["exception"].value),
                'traceback': traceback.format_exception(
                    record["exception"].type,
                    record["exception"].value,
                    record["exception"].traceback
                )
            }

        return json.dumps(log_data, ensure_ascii=False, default=str) + '\n'

    def _format_access_log(self, record):
        """格式化访问日志"""
        access_data = {
            'timestamp': record["time"].isoformat(),
            'type': 'access',
            'request_id': record["extra"].get('request_id', ''),
            'method': record["extra"].get('method', ''),
            'path': record["extra"].get('path', ''),
            'status_code': record["extra"].get('status_code', 0),
            'response_time': record["extra"].get('response_time', 0),
            'user_agent': record["extra"].get('user_agent', ''),
            'client_ip': record["extra"].get('client_ip', ''),
            'user_id': record["extra"].get('user_id', ''),
        }

        return json.dumps(access_data, ensure_ascii=False, default=str) + '\n'

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.bind(**kwargs).info(message)

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.bind(**kwargs).warning(message)

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self.logger.bind(**kwargs).error(message)

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.bind(**kwargs).debug(message)

    def exception(self, message: str, **kwargs):
        """记录异常日志"""
        self.logger.bind(**kwargs).exception(message)

    def access(self, **kwargs):
        """记录访问日志"""
        self.logger.bind(log_type="access", **kwargs).info("API访问")

    def bind(self, **kwargs):
        """绑定上下文信息"""
        return self.logger.bind(**kwargs)


# 全局日志实例
structured_logger = StructuredLogger()


# 日志装饰器
def log_execution_time(func_name: Optional[str] = None, log_args: bool = False):
    """记录函数执行时间装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func_name or f"{func.__module__}.{func.__name__}"
            
            log_data = {'function': function_name}
            if log_args:
                log_data['args'] = str(args)
                log_data['kwargs'] = str(kwargs)

            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time

                structured_logger.info(
                    f"函数执行完成: {function_name}",
                    execution_time=execution_time,
                    status="success",
                    **log_data
                )

                return result
            except Exception as e:
                execution_time = time.time() - start_time

                structured_logger.error(
                    f"函数执行失败: {function_name}",
                    execution_time=execution_time,
                    status="error",
                    error=str(e),
                    **log_data
                )

                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func_name or f"{func.__module__}.{func.__name__}"
            
            log_data = {'function': function_name}
            if log_args:
                log_data['args'] = str(args)
                log_data['kwargs'] = str(kwargs)

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                structured_logger.info(
                    f"函数执行完成: {function_name}",
                    execution_time=execution_time,
                    status="success",
                    **log_data
                )

                return result
            except Exception as e:
                execution_time = time.time() - start_time

                structured_logger.error(
                    f"函数执行失败: {function_name}",
                    execution_time=execution_time,
                    status="error",
                    error=str(e),
                    **log_data
                )

                raise

        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def log_api_call(operation: str = None):
    """API调用日志装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation or func.__name__
            request_id = request_id_var.get('')
            
            structured_logger.info(
                f"API调用开始: {op_name}",
                operation=op_name,
                request_id=request_id
            )
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                structured_logger.info(
                    f"API调用成功: {op_name}",
                    operation=op_name,
                    execution_time=execution_time,
                    request_id=request_id
                )
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                
                structured_logger.error(
                    f"API调用失败: {op_name}",
                    operation=op_name,
                    execution_time=execution_time,
                    error=str(e),
                    request_id=request_id
                )
                
                raise
        
        return wrapper
    return decorator


# 上下文管理器
class LogContext:
    """日志上下文管理器"""

    def __init__(self, **context):
        self.context = context
        self.tokens = []

    def __enter__(self):
        for key, value in self.context.items():
            if key == 'request_id':
                token = request_id_var.set(value)
            elif key == 'user_id':
                token = user_id_var.set(value)
            elif key == 'trace_id':
                token = trace_id_var.set(value)
            else:
                continue
            self.tokens.append((key, token))
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        for key, token in reversed(self.tokens):
            if key == 'request_id':
                request_id_var.reset(token)
            elif key == 'user_id':
                user_id_var.reset(token)
            elif key == 'trace_id':
                trace_id_var.reset(token)
