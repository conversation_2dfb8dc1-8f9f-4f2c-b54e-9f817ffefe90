from fastapi import APIRouter, BackgroundTasks, Depends, File, Query, Request, UploadFile
from apps.admin.services.common import CommonService
from apps.admin.services.login import LoginService
from core.logging.manager import logger
from common.response import ResponseService

router = APIRouter(prefix='/common', dependencies=[Depends(LoginService.get_current_user)])


@router.post('/upload')
async def common_upload(request: Request, file: UploadFile = File(...)):
    upload_result = await CommonService.upload_service(request, file)
    logger.info('上传成功')

    return ResponseService.success(model_content=upload_result.result)


@router.get('/download')
async def common_download(
    request: Request,
    background_tasks: BackgroundTasks,
    file_name: str = Query(alias='fileName'),
    delete: bool = Query(),
):
    download_result = await CommonService.download_services(background_tasks, file_name, delete)
    logger.info(download_result.message)

    return ResponseService.streaming(data=download_result.result)


@router.get('/download/resource')
async def common_download_resource(request: Request, resource: str = Query()):
    download_resource_result = await CommonService.download_resource_services(resource)
    logger.info(download_resource_result.message)

    return ResponseService.streaming(data=download_resource_result.result)
