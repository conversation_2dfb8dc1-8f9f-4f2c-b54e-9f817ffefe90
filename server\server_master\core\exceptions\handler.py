"""
异常处理器模块
"""
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

from .error_codes import ErrorCode, ErrorCategory
from .exceptions import StandardException
from ..logging.structured_logger import structured_logger


class ExceptionHandler:
    """异常处理器"""

    @staticmethod
    def format_error_response(
        error_code: ErrorCode, 
        detail: Optional[str] = None, 
        data: Optional[Any] = None,
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            'success': False,
            'code': error_code.code,
            'message': detail or error_code.message,
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'trace_id': trace_id or 'unknown'
        }

    @staticmethod
    def get_http_status_code(error_code: ErrorCode) -> int:
        """根据错误码获取HTTP状态码"""
        if error_code.code == 2000:  # UNAUTHORIZED
            return status.HTTP_401_UNAUTHORIZED
        elif error_code.code == 2003:  # PERMISSION_DENIED
            return status.HTTP_403_FORBIDDEN
        elif error_code.code == 3001:  # RESOURCE_NOT_FOUND
            return status.HTTP_404_NOT_FOUND
        elif error_code.code == 3002:  # RESOURCE_ALREADY_EXISTS
            return status.HTTP_409_CONFLICT
        elif error_code.code == 4001:  # RATE_LIMIT_EXCEEDED
            return status.HTTP_429_TOO_MANY_REQUESTS
        elif ErrorCategory.is_client_error(error_code):
            return status.HTTP_400_BAD_REQUEST
        else:
            return status.HTTP_500_INTERNAL_SERVER_ERROR

    @staticmethod
    async def handle_standard_exception(request: Request, exc: StandardException) -> JSONResponse:
        """处理标准异常"""
        structured_logger.error(
            f"标准异常: {exc.error_code.name}",
            error_code=exc.error_code.code,
            detail=exc.detail,
            trace_id=exc.trace_id,
            path=request.url.path,
            method=request.method
        )

        response_data = ExceptionHandler.format_error_response(
            exc.error_code, 
            exc.detail, 
            exc.data,
            exc.trace_id
        )
        
        status_code = ExceptionHandler.get_http_status_code(exc.error_code)
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )

    @staticmethod
    async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
        """处理HTTP异常"""
        structured_logger.warning(
            f"HTTP异常: {exc.status_code}",
            detail=exc.detail,
            path=request.url.path,
            method=request.method
        )

        # 映射HTTP状态码到错误码
        if exc.status_code == 401:
            error_code = ErrorCode.UNAUTHORIZED
        elif exc.status_code == 403:
            error_code = ErrorCode.PERMISSION_DENIED
        elif exc.status_code == 404:
            error_code = ErrorCode.RESOURCE_NOT_FOUND
        elif exc.status_code == 429:
            error_code = ErrorCode.RATE_LIMIT_EXCEEDED
        else:
            error_code = ErrorCode.BAD_REQUEST

        response_data = ExceptionHandler.format_error_response(
            error_code, 
            exc.detail
        )

        return JSONResponse(
            status_code=exc.status_code,
            content=response_data
        )

    @staticmethod
    async def handle_validation_error(request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求验证错误"""
        structured_logger.warning(
            "请求验证失败",
            errors=exc.errors(),
            path=request.url.path,
            method=request.method
        )

        # 格式化验证错误信息
        error_details = []
        for error in exc.errors():
            field = '.'.join(str(loc) for loc in error['loc'])
            message = error['msg']
            error_details.append(f"{field}: {message}")

        response_data = ExceptionHandler.format_error_response(
            ErrorCode.VALIDATION_ERROR,
            "请求参数验证失败",
            {'errors': error_details}
        )

        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response_data
        )

    @staticmethod
    async def handle_sqlalchemy_error(request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """处理SQLAlchemy错误"""
        structured_logger.error(
            "数据库操作异常",
            error=str(exc),
            path=request.url.path,
            method=request.method,
            exc_info=True
        )

        response_data = ExceptionHandler.format_error_response(
            ErrorCode.DATABASE_ERROR,
            "数据库操作失败"
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_data
        )

    @staticmethod
    async def handle_generic_exception(request: Request, exc: Exception) -> JSONResponse:
        """处理通用异常"""
        structured_logger.exception(
            f"未处理的异常: {type(exc).__name__}",
            error=str(exc),
            path=request.url.path,
            method=request.method
        )

        response_data = ExceptionHandler.format_error_response(
            ErrorCode.SYSTEM_ERROR,
            "系统内部错误"
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_data
        )


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(StandardException)
    async def standard_exception_handler(request: Request, exc: StandardException):
        return await ExceptionHandler.handle_standard_exception(request, exc)

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return await ExceptionHandler.handle_http_exception(request, exc)

    @app.exception_handler(StarletteHTTPException)
    async def starlette_http_exception_handler(request: Request, exc: StarletteHTTPException):
        return await ExceptionHandler.handle_http_exception(request, HTTPException(status_code=exc.status_code, detail=exc.detail))

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return await ExceptionHandler.handle_validation_error(request, exc)

    @app.exception_handler(SQLAlchemyError)
    async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
        return await ExceptionHandler.handle_sqlalchemy_error(request, exc)

    @app.exception_handler(Exception)
    async def generic_exception_handler(request: Request, exc: Exception):
        return await ExceptionHandler.handle_generic_exception(request, exc)

    structured_logger.info("异常处理器设置完成")


# 异常上下文管理器
class ExceptionContext:
    """异常上下文管理器"""

    def __init__(self, operation: str, **context):
        self.operation = operation
        self.context = context

    async def __aenter__(self):
        structured_logger.debug(f"开始操作: {self.operation}", **self.context)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            if isinstance(exc_val, StandardException):
                exc_val.context.update(self.context)
                exc_val.context['operation'] = self.operation
            
            structured_logger.error(
                f"操作失败: {self.operation}",
                error=str(exc_val),
                **self.context
            )
        else:
            structured_logger.debug(f"操作完成: {self.operation}", **self.context)
        
        return False  # 不抑制异常


# 异常装饰器
def handle_exceptions(operation: str = None, reraise: bool = True):
    """异常处理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            
            try:
                return await func(*args, **kwargs)
            except StandardException:
                if reraise:
                    raise
            except Exception as e:
                structured_logger.exception(f"操作异常: {op_name}", error=str(e))
                if reraise:
                    raise StandardException(ErrorCode.SYSTEM_ERROR, f"操作失败: {op_name}", cause=e)
        
        return wrapper
    return decorator
