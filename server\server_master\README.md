# MXTT-FastAPI 企业级后台管理系统

## 项目简介

MXTT-FastAPI 是一个基于 FastAPI 的现代化企业级后台管理系统，采用分层架构设计，具有高性能、高安全性、高可维护性的特点。

## 技术栈

### 后端技术
- **FastAPI**: 现代化的 Python Web 框架
- **SQLAlchemy**: 异步 ORM 框架
- **Pydantic**: 数据验证和序列化
- **Alembic**: 数据库迁移工具
- **Redis**: 缓存和会话存储
- **JWT**: 身份认证
- **Loguru**: 结构化日志

### 数据库
- **MySQL 8.0**: 主数据库
- **Redis 7**: 缓存数据库

### 部署运维
- **Docker**: 容器化部署
- **Docker Compose**: 服务编排
- **Nginx**: 反向代理
- **Prometheus**: 监控指标收集
- **Grafana**: 监控可视化

## 项目特性

### 🏗️ 架构设计
- **分层架构**: Controller → Service → Repository → Model
- **依赖注入**: 自动管理组件依赖关系
- **异常标准化**: 统一错误处理和响应格式
- **配置管理**: 环境分离、参数验证

### 🚀 性能优化
- **异步编程**: 全异步架构，高并发处理
- **连接池优化**: 数据库连接池调优
- **查询优化**: 游标分页、批量操作、预加载
- **缓存策略**: 多级缓存、缓存预热

### 🔒 安全增强
- **JWT认证**: 访问令牌 + 刷新令牌机制
- **权限控制**: RBAC权限模型 + 数据权限
- **API限流**: 多种限流算法支持
- **安全头**: XSS、CSRF、点击劫持防护

### 📊 可观测性
- **结构化日志**: 链路追踪、请求ID、用户ID上下文
- **性能监控**: 响应时间、错误率、吞吐量监控
- **健康检查**: 数据库、缓存、系统资源检查
- **指标收集**: Prometheus + Grafana 监控面板

## 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7+

### 开发环境部署

1. **克隆项目**
```bash
git clone <repository-url>
cd server/server_master
```

2. **配置环境变量**
```bash
cp .env.development .env
# 根据实际情况修改配置
```

3. **启动开发环境**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev
```

4. **访问应用**
- 应用地址: http://localhost:9099
- API文档: http://localhost:9099/docs
- 数据库管理: http://localhost:8080
- Redis管理: http://localhost:8081

### 生产环境部署

1. **配置生产环境**
```bash
cp .env.production.example .env.production
# 修改生产环境配置
```

2. **部署生产环境**
```bash
./scripts/deploy.sh prod --build
```

3. **访问服务**
- 应用地址: http://localhost:9099
- Nginx代理: http://localhost
- 监控面板: http://localhost:3000
- 指标收集: http://localhost:9090

## 项目结构

```
server_master/
├── apps/                   # 业务应用模块
│   └── admin/             # 管理后台模块
│       ├── controllers/   # 控制器层
│       ├── services/      # 服务层
│       ├── repositories/  # 仓储层
│       ├── models/        # 数据模型
│       └── schemas/       # 数据传输对象
├── core/                  # 核心框架
│   ├── base/             # 基础抽象类
│   ├── database/         # 数据库管理
│   ├── cache/            # 缓存管理
│   ├── security/         # 安全组件
│   ├── exceptions/       # 异常处理
│   ├── logging/          # 日志系统
│   ├── monitoring/       # 监控系统
│   └── middleware/       # 中间件
├── config/               # 配置管理
├── scripts/              # 部署脚本
├── logs/                 # 日志文件
├── storage/              # 文件存储
└── tests/                # 测试用例
```

## API 文档

### 认证接口
- `POST /api/login` - 用户登录
- `POST /api/logout` - 用户登出
- `POST /api/refresh` - 刷新令牌
- `GET /api/getInfo` - 获取用户信息
- `GET /api/captchaImage` - 获取验证码

### 用户管理
- `GET /api/system/user/list` - 获取用户列表
- `POST /api/system/user` - 创建用户
- `PUT /api/system/user/{id}` - 更新用户
- `DELETE /api/system/user/{id}` - 删除用户
- `GET /api/system/user/{id}` - 获取用户详情

### 系统监控
- `GET /health` - 健康检查
- `GET /metrics` - 系统指标

## 开发指南

### 添加新的业务模块

1. **创建模型**
```python
# apps/your_module/models/your_model.py
from sqlalchemy import Column, Integer, String
from core.database.base import Base

class YourModel(Base):
    __tablename__ = 'your_table'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
```

2. **创建数据传输对象**
```python
# apps/your_module/schemas/your_schema.py
from pydantic import BaseModel

class YourCreateSchema(BaseModel):
    name: str

class YourResponseSchema(BaseModel):
    id: int
    name: str
```

3. **创建仓储层**
```python
# apps/your_module/repositories/your_repository.py
from core.base.repository import SQLAlchemyRepository
from core.dependencies import repository

@repository()
class YourRepository(SQLAlchemyRepository):
    pass
```

4. **创建服务层**
```python
# apps/your_module/services/your_service.py
from core.base.service import CRUDService
from core.dependencies import service

@service()
class YourService(CRUDService):
    pass
```

5. **创建控制器**
```python
# apps/your_module/controllers/your_controller.py
from core.base.controller import CRUDController

class YourController(CRUDController):
    pass
```

### 配置管理

环境配置文件：
- `.env.development` - 开发环境
- `.env.testing` - 测试环境
- `.env.production` - 生产环境

配置类：
- `AppConfig` - 应用配置
- `DatabaseConfig` - 数据库配置
- `RedisConfig` - Redis配置

### 日志使用

```python
from core.logging.structured_logger import structured_logger

# 基础日志
structured_logger.info("操作成功", user_id=123, action="create")
structured_logger.error("操作失败", error=str(e))

# 装饰器日志
@log_execution_time("用户创建")
async def create_user(user_data):
    pass

@log_api_call("获取用户列表")
async def get_users():
    pass
```

### 缓存使用

```python
from core.cache.cache_manager import cache_result, cache_invalidate

# 缓存结果
@cache_result("user_list", ttl=300)
async def get_user_list():
    pass

# 缓存失效
@cache_invalidate("user_*")
async def update_user():
    pass
```

## 部署运维

### Docker 命令

```bash
# 开发环境
./scripts/deploy.sh dev              # 启动开发环境
./scripts/deploy.sh dev --build      # 重新构建并启动
./scripts/deploy.sh dev --logs       # 查看日志
./scripts/deploy.sh dev --stop       # 停止服务

# 生产环境
./scripts/deploy.sh prod             # 启动生产环境
./scripts/deploy.sh prod --build     # 重新构建并启动
./scripts/deploy.sh prod --restart   # 重启服务
./scripts/deploy.sh prod --clean     # 清理资源
```

### 监控指标

- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求数、响应时间、错误率
- **数据库指标**: 连接池状态、查询性能
- **缓存指标**: 命中率、连接数

### 日志管理

日志文件位置：
- `logs/app_*.log` - 应用日志
- `logs/error_*.log` - 错误日志
- `logs/access_*.log` - 访问日志

日志轮转：每天轮转，保留30天

## 测试

### 运行测试
```bash
# 单元测试
pytest tests/unit/

# 集成测试
pytest tests/integration/

# 覆盖率测试
pytest --cov=apps --cov=core tests/
```

### 性能测试
```bash
# 使用 locust 进行压力测试
locust -f tests/performance/locustfile.py
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 文档地址: [Documentation]

## 更新日志

### v2.0.0 (2024-12-XX)
- 🎉 全新架构重构
- ⚡ 性能优化提升
- 🔒 安全性增强
- 📊 监控系统完善
- 🐳 Docker容器化部署
