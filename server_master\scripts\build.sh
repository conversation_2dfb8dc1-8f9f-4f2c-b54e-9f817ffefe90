#!/bin/bash

# MXTT-FastAPI 构建脚本
# 用于构建前后端集成项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "MXTT-FastAPI 构建脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --frontend      只构建前端"
    echo "  --backend       只构建后端"
    echo "  --all           构建前后端（默认）"
    echo "  --clean         清理构建文件"
    echo "  --dev           开发模式构建"
    echo "  --prod          生产模式构建"
    echo "  --docker        Docker构建"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --all --prod        # 生产模式构建前后端"
    echo "  $0 --frontend --dev    # 开发模式构建前端"
    echo "  $0 --docker            # Docker构建"
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python3"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装 pip3"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 清理构建文件
clean_build() {
    log_info "清理构建文件..."
    
    # 清理前端构建文件
    if [ -d "../admin/dist" ]; then
        rm -rf ../admin/dist
        log_info "清理前端构建文件完成"
    fi
    
    # 清理Python缓存
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -type f -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理日志文件
    if [ -d "logs" ]; then
        rm -rf logs/*
        log_info "清理日志文件完成"
    fi
    
    # 清理临时文件
    if [ -d "storage/temp" ]; then
        rm -rf storage/temp/*
        log_info "清理临时文件完成"
    fi
    
    log_info "构建文件清理完成"
}

# 构建前端
build_frontend() {
    local mode=$1
    log_info "开始构建前端..."
    
    # 检查前端目录
    if [ ! -d "../admin" ]; then
        log_error "前端目录不存在: ../admin"
        exit 1
    fi
    
    cd ../admin
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    # 构建前端
    if [ "$mode" = "dev" ]; then
        log_info "开发模式构建前端..."
        npm run build:dev
    else
        log_info "生产模式构建前端..."
        npm run build:prod
    fi
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    # 复制构建文件到后端静态目录
    log_info "复制前端构建文件..."
    mkdir -p ../server_master/static
    cp -r dist/* ../server_master/static/
    
    cd ../server_master
    
    log_info "前端构建完成"
}

# 构建后端
build_backend() {
    local mode=$1
    log_info "开始构建后端..."
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    log_info "安装后端依赖..."
    pip install -r requirements.txt
    
    # 运行代码检查
    if [ "$mode" = "prod" ]; then
        log_info "运行代码质量检查..."
        
        # 代码格式化检查
        if command -v black &> /dev/null; then
            black --check . || log_warn "代码格式化检查发现问题"
        fi
        
        # 导入排序检查
        if command -v isort &> /dev/null; then
            isort --check-only . || log_warn "导入排序检查发现问题"
        fi
        
        # 代码风格检查
        if command -v flake8 &> /dev/null; then
            flake8 . || log_warn "代码风格检查发现问题"
        fi
    fi
    
    # 运行测试
    if [ "$mode" = "prod" ]; then
        log_info "运行单元测试..."
        if command -v pytest &> /dev/null; then
            pytest tests/unit/ -v || log_warn "单元测试发现问题"
        fi
    fi
    
    # 生成requirements.txt（如果需要）
    if [ "$mode" = "prod" ]; then
        log_info "生成依赖文件..."
        pip freeze > requirements-freeze.txt
    fi
    
    log_info "后端构建完成"
}

# Docker构建
build_docker() {
    local mode=$1
    log_info "开始Docker构建..."
    
    # 构建前端
    build_frontend $mode
    
    # 构建Docker镜像
    if [ "$mode" = "dev" ]; then
        log_info "构建开发环境Docker镜像..."
        docker build -f Dockerfile.dev -t mxtt-fastapi:dev .
    else
        log_info "构建生产环境Docker镜像..."
        docker build -f Dockerfile -t mxtt-fastapi:latest .
    fi
    
    log_info "Docker构建完成"
}

# 创建部署包
create_deployment_package() {
    local mode=$1
    log_info "创建部署包..."
    
    # 创建部署目录
    local deploy_dir="deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p $deploy_dir
    
    # 复制必要文件
    cp -r apps $deploy_dir/
    cp -r core $deploy_dir/
    cp -r config $deploy_dir/
    cp -r common $deploy_dir/
    cp -r static $deploy_dir/ 2>/dev/null || true
    cp main.py $deploy_dir/
    cp requirements.txt $deploy_dir/
    cp docker-compose.yml $deploy_dir/
    cp -r scripts $deploy_dir/
    
    # 复制环境配置文件
    if [ "$mode" = "prod" ]; then
        cp .env.production.example $deploy_dir/.env.production
    else
        cp .env.development.example $deploy_dir/.env.development
    fi
    
    # 创建部署说明
    cat > $deploy_dir/README.md << EOF
# MXTT-FastAPI 部署包

## 部署说明

1. 配置环境变量文件
2. 运行部署脚本: ./scripts/deploy.sh $mode
3. 访问应用: http://localhost:9099

## 目录结构

- apps/: 业务应用模块
- core/: 核心框架
- config/: 配置文件
- static/: 前端静态文件
- scripts/: 部署脚本

构建时间: $(date)
构建模式: $mode
EOF
    
    # 打包
    tar -czf ${deploy_dir}.tar.gz $deploy_dir
    rm -rf $deploy_dir
    
    log_info "部署包创建完成: ${deploy_dir}.tar.gz"
}

# 主函数
main() {
    local build_target="all"
    local build_mode="prod"
    local docker_build=false
    local clean_only=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --frontend)
                build_target="frontend"
                shift
                ;;
            --backend)
                build_target="backend"
                shift
                ;;
            --all)
                build_target="all"
                shift
                ;;
            --clean)
                clean_only=true
                shift
                ;;
            --dev)
                build_mode="dev"
                shift
                ;;
            --prod)
                build_mode="prod"
                shift
                ;;
            --docker)
                docker_build=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始构建 MXTT-FastAPI 项目"
    log_info "构建目标: $build_target"
    log_info "构建模式: $build_mode"
    
    # 检查依赖
    check_dependencies
    
    # 清理构建文件
    if [ "$clean_only" = true ]; then
        clean_build
        exit 0
    fi
    
    clean_build
    
    # Docker构建
    if [ "$docker_build" = true ]; then
        build_docker $build_mode
        exit 0
    fi
    
    # 根据目标构建
    case $build_target in
        frontend)
            build_frontend $build_mode
            ;;
        backend)
            build_backend $build_mode
            ;;
        all)
            build_frontend $build_mode
            build_backend $build_mode
            ;;
    esac
    
    # 创建部署包（生产模式）
    if [ "$build_mode" = "prod" ]; then
        create_deployment_package $build_mode
    fi
    
    log_info "构建完成！"
}

# 执行主函数
main "$@"
