"""
核心框架模块
"""
from .application import ApplicationFactory, create_app
from .base import BaseService, BaseRepository, BaseController
from .dependencies import get_db, get_service, service, repository, singleton
from .exceptions import (
    ErrorCode, StandardException, BusinessException, 
    AuthenticationException, ValidationException
)
from .logging import structured_logger, log_execution_time, log_api_call

__all__ = [
    # 应用工厂
    'ApplicationFactory',
    'create_app',
    
    # 基础类
    'BaseService',
    'BaseRepository', 
    'BaseController',
    
    # 依赖注入
    'get_db',
    'get_service',
    'service',
    'repository',
    'singleton',
    
    # 异常处理
    'ErrorCode',
    'StandardException',
    'BusinessException',
    'AuthenticationException',
    'ValidationException',
    
    # 日志系统
    'structured_logger',
    'log_execution_time',
    'log_api_call'
]
