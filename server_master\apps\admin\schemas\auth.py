"""
认证相关数据传输对象模块
"""
import re
from typing import Optional, List
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic.alias_generators import to_camel

from core.exceptions import ValidationException


class LoginSchema(BaseModel):
    """登录模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    username: str = Field(..., description='用户名', max_length=30)
    password: str = Field(..., description='密码', max_length=20)
    code: Optional[str] = Field(default=None, description='验证码')
    uuid: Optional[str] = Field(default=None, description='验证码UUID')
    remember_me: Optional[bool] = Field(default=False, description='记住我')

    @field_validator('username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValidationException('用户名不能为空')
        if len(v) > 30:
            raise ValidationException('用户名长度不能超过30个字符')
        return v.strip()

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if not v:
            raise ValidationException('密码不能为空')
        if len(v) > 20:
            raise ValidationException('密码长度不能超过20个字符')
        # 检查非法字符
        if re.search(r'[<>"\'|\\]', v):
            raise ValidationException('密码不能包含非法字符：< > " \' \\ |')
        return v


class RegisterSchema(BaseModel):
    """注册模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    username: str = Field(..., description='用户名', max_length=30)
    password: str = Field(..., description='密码', min_length=6, max_length=20)
    confirm_password: str = Field(..., description='确认密码')
    code: Optional[str] = Field(default=None, description='验证码')
    uuid: Optional[str] = Field(default=None, description='验证码UUID')

    @field_validator('username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValidationException('用户名不能为空')
        if len(v) > 30:
            raise ValidationException('用户名长度不能超过30个字符')
        # 检查脚本字符
        if re.search(r'[<>"\'/\\]', v):
            raise ValidationException('用户名不能包含脚本字符')
        return v.strip()

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if not v or len(v) < 6:
            raise ValidationException('密码长度不能少于6个字符')
        if len(v) > 20:
            raise ValidationException('密码长度不能超过20个字符')
        # 检查非法字符
        if re.search(r'[<>"\'|\\]', v):
            raise ValidationException('密码不能包含非法字符：< > " \' \\ |')
        return v

    def validate_passwords_match(self) -> 'RegisterSchema':
        """验证两次密码是否一致"""
        if self.password != self.confirm_password:
            raise ValidationException('两次输入的密码不一致')
        return self


class TokenSchema(BaseModel):
    """令牌模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    access_token: str = Field(description='访问令牌')
    refresh_token: str = Field(description='刷新令牌')
    token_type: str = Field(default='bearer', description='令牌类型')
    expires_in: int = Field(description='过期时间（秒）')


class RefreshTokenSchema(BaseModel):
    """刷新令牌模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    refresh_token: str = Field(..., description='刷新令牌')


class LogoutSchema(BaseModel):
    """登出模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    token: Optional[str] = Field(default=None, description='访问令牌')


class CaptchaSchema(BaseModel):
    """验证码模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    uuid: str = Field(description='验证码UUID')
    img: str = Field(description='验证码图片（Base64）')
    captcha_enabled: bool = Field(description='是否启用验证码')


class UserInfoSchema(BaseModel):
    """用户信息模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    user_id: int = Field(description='用户ID')
    user_name: str = Field(description='用户名')
    nick_name: str = Field(description='用户昵称')
    avatar: Optional[str] = Field(description='头像')
    email: Optional[str] = Field(description='邮箱')
    phonenumber: Optional[str] = Field(description='手机号')
    sex: Optional[str] = Field(description='性别')
    dept_id: Optional[int] = Field(description='部门ID')
    dept_name: Optional[str] = Field(description='部门名称')
    roles: List[str] = Field(default=[], description='角色列表')
    permissions: List[str] = Field(default=[], description='权限列表')


class PasswordChangeSchema(BaseModel):
    """修改密码模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    old_password: str = Field(..., description='旧密码')
    new_password: str = Field(..., description='新密码', min_length=6, max_length=20)
    confirm_password: str = Field(..., description='确认新密码')

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        if not v or len(v) < 6:
            raise ValidationException('新密码长度不能少于6个字符')
        if len(v) > 20:
            raise ValidationException('新密码长度不能超过20个字符')
        if re.search(r'[<>"\'|\\]', v):
            raise ValidationException('新密码不能包含非法字符：< > " \' \\ |')
        return v

    def validate_passwords_match(self) -> 'PasswordChangeSchema':
        """验证两次密码是否一致"""
        if self.new_password != self.confirm_password:
            raise ValidationException('两次输入的新密码不一致')
        return self


class LoginLogSchema(BaseModel):
    """登录日志模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    info_id: int = Field(description='访问ID')
    user_name: str = Field(description='用户账号')
    ipaddr: str = Field(description='登录IP地址')
    login_location: Optional[str] = Field(description='登录地点')
    browser: Optional[str] = Field(description='浏览器类型')
    os: Optional[str] = Field(description='操作系统')
    status: str = Field(description='登录状态（0成功 1失败）')
    msg: Optional[str] = Field(description='提示消息')
    login_time: str = Field(description='访问时间')


class OnlineUserSchema(BaseModel):
    """在线用户模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    token_id: str = Field(description='会话编号')
    user_name: str = Field(description='用户名称')
    dept_name: Optional[str] = Field(description='部门名称')
    ipaddr: str = Field(description='登录IP地址')
    login_location: Optional[str] = Field(description='登录地点')
    browser: Optional[str] = Field(description='浏览器类型')
    os: Optional[str] = Field(description='操作系统')
    login_time: str = Field(description='登录时间')


class ForceLogoutSchema(BaseModel):
    """强制退出模式"""
    
    model_config = ConfigDict(alias_generator=to_camel)

    token_id: str = Field(..., description='会话编号')
