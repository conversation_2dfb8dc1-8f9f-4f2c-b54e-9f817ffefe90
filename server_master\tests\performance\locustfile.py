"""
性能测试脚本 - 使用Locust
"""
import json
import random
from locust import HttpUser, task, between


class AdminUser(HttpUser):
    """管理后台用户行为模拟"""
    
    wait_time = between(1, 3)  # 用户操作间隔时间
    
    def on_start(self):
        """用户开始时的初始化操作"""
        self.login()
    
    def login(self):
        """用户登录"""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        with self.client.post("/api/login", json=login_data, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    # 保存访问令牌
                    self.token = data["data"]["access_token"]
                    self.client.headers.update({"Authorization": f"Bearer {self.token}"})
                    response.success()
                else:
                    response.failure(f"登录失败: {data.get('message')}")
            else:
                response.failure(f"登录请求失败: {response.status_code}")
    
    @task(3)
    def get_user_list(self):
        """获取用户列表 - 高频操作"""
        params = {
            "pageNum": random.randint(1, 5),
            "pageSize": random.choice([10, 20, 50])
        }
        
        with self.client.get("/api/system/user/list", params=params, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if "rows" in data and "total" in data:
                    response.success()
                else:
                    response.failure("响应数据格式错误")
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(2)
    def get_user_detail(self):
        """获取用户详情"""
        user_id = random.randint(1, 100)  # 假设用户ID范围
        
        with self.client.get(f"/api/system/user/{user_id}", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                # 用户不存在也算正常情况
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(1)
    def create_user(self):
        """创建用户 - 低频操作"""
        user_data = {
            "userName": f"test_user_{random.randint(1000, 9999)}",
            "nickName": f"测试用户{random.randint(1, 1000)}",
            "email": f"test{random.randint(1000, 9999)}@example.com",
            "phonenumber": f"138{random.randint(10000000, 99999999)}",
            "password": "test123456",
            "status": "0"
        }
        
        with self.client.post("/api/system/user/", json=user_data, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"创建用户失败: {data.get('message')}")
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(1)
    def update_user(self):
        """更新用户"""
        user_id = random.randint(1, 100)
        update_data = {
            "nickName": f"更新用户{random.randint(1, 1000)}",
            "email": f"updated{random.randint(1000, 9999)}@example.com"
        }
        
        with self.client.put(f"/api/system/user/{user_id}", json=update_data, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(2)
    def get_role_list(self):
        """获取角色列表"""
        with self.client.get("/api/system/role/list", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(2)
    def get_menu_list(self):
        """获取菜单列表"""
        with self.client.get("/api/system/menu/list", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(1)
    def get_dept_list(self):
        """获取部门列表"""
        with self.client.get("/api/system/dept/list", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(1)
    def get_system_info(self):
        """获取系统信息"""
        with self.client.get("/api/getInfo", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"请求失败: {response.status_code}")
    
    @task(1)
    def health_check(self):
        """健康检查"""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"健康检查失败: {response.status_code}")


class ApiUser(HttpUser):
    """API用户行为模拟 - 专注于API性能测试"""
    
    wait_time = between(0.5, 2)
    
    def on_start(self):
        """初始化"""
        self.login()
    
    def login(self):
        """登录获取令牌"""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = self.client.post("/api/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                self.token = data["data"]["access_token"]
                self.client.headers.update({"Authorization": f"Bearer {self.token}"})
    
    @task(5)
    def concurrent_user_list(self):
        """并发用户列表查询"""
        params = {
            "pageNum": random.randint(1, 10),
            "pageSize": 20
        }
        self.client.get("/api/system/user/list", params=params)
    
    @task(3)
    def concurrent_user_search(self):
        """并发用户搜索"""
        search_terms = ["admin", "test", "user", "管理", "测试"]
        params = {
            "userName": random.choice(search_terms),
            "pageNum": 1,
            "pageSize": 10
        }
        self.client.get("/api/system/user/list", params=params)
    
    @task(2)
    def concurrent_user_detail(self):
        """并发用户详情查询"""
        user_id = random.randint(1, 50)
        self.client.get(f"/api/system/user/{user_id}")
    
    @task(1)
    def concurrent_user_operations(self):
        """并发用户操作"""
        # 随机选择操作类型
        operations = ["create", "update", "delete"]
        operation = random.choice(operations)
        
        if operation == "create":
            user_data = {
                "userName": f"perf_user_{random.randint(10000, 99999)}",
                "nickName": f"性能测试用户{random.randint(1, 1000)}",
                "email": f"perf{random.randint(10000, 99999)}@example.com",
                "password": "test123456"
            }
            self.client.post("/api/system/user/", json=user_data)
        
        elif operation == "update":
            user_id = random.randint(1, 100)
            update_data = {
                "nickName": f"更新用户{random.randint(1, 1000)}"
            }
            self.client.put(f"/api/system/user/{user_id}", json=update_data)
        
        elif operation == "delete":
            user_id = random.randint(50, 100)  # 避免删除重要用户
            self.client.delete(f"/api/system/user/{user_id}")


class DatabaseStressUser(HttpUser):
    """数据库压力测试用户"""
    
    wait_time = between(0.1, 0.5)  # 更短的等待时间，增加压力
    
    def on_start(self):
        self.login()
    
    def login(self):
        login_data = {"username": "admin", "password": "admin123"}
        response = self.client.post("/api/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                self.token = data["data"]["access_token"]
                self.client.headers.update({"Authorization": f"Bearer {self.token}"})
    
    @task(10)
    def stress_user_list(self):
        """用户列表压力测试"""
        params = {
            "pageNum": random.randint(1, 20),
            "pageSize": random.choice([50, 100])
        }
        self.client.get("/api/system/user/list", params=params)
    
    @task(5)
    def stress_complex_query(self):
        """复杂查询压力测试"""
        params = {
            "userName": f"test_{random.randint(1, 100)}",
            "nickName": "测试",
            "status": random.choice(["0", "1"]),
            "pageNum": random.randint(1, 5),
            "pageSize": 50
        }
        self.client.get("/api/system/user/list", params=params)
    
    @task(3)
    def stress_batch_operations(self):
        """批量操作压力测试"""
        # 批量查询用户详情
        user_ids = [random.randint(1, 100) for _ in range(5)]
        for user_id in user_ids:
            self.client.get(f"/api/system/user/{user_id}")


# 性能测试配置
class PerformanceTestConfig:
    """性能测试配置"""
    
    # 测试场景配置
    SCENARIOS = {
        "light_load": {
            "users": 10,
            "spawn_rate": 2,
            "duration": "5m"
        },
        "normal_load": {
            "users": 50,
            "spawn_rate": 5,
            "duration": "10m"
        },
        "heavy_load": {
            "users": 100,
            "spawn_rate": 10,
            "duration": "15m"
        },
        "stress_test": {
            "users": 200,
            "spawn_rate": 20,
            "duration": "20m"
        }
    }
    
    # 性能指标阈值
    THRESHOLDS = {
        "avg_response_time": 200,  # 平均响应时间 < 200ms
        "p95_response_time": 500,  # 95%响应时间 < 500ms
        "error_rate": 0.01,        # 错误率 < 1%
        "requests_per_second": 100  # 每秒请求数 > 100
    }
