"""
性能监控指标模块
"""
import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict, deque

from ..logging.structured_logger import structured_logger


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_usage: float
    disk_used: int
    disk_total: int
    network_bytes_sent: int
    network_bytes_recv: int
    load_average: List[float]
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ApplicationMetrics:
    """应用指标"""
    request_count: int
    response_time_avg: float
    response_time_p95: float
    response_time_p99: float
    error_rate: float
    active_connections: int
    cache_hit_rate: float
    db_connection_pool_size: int
    db_connection_pool_used: int
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class MetricsCollector:
    """指标收集器"""

    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.request_times = deque(maxlen=max_samples)
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.endpoint_metrics = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'error_count': 0,
            'response_times': deque(maxlen=100)
        })

    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络IO
            network = psutil.net_io_counters()
            
            # 负载平均值
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0.0, 0.0, 0.0]

            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used=memory.used,
                memory_total=memory.total,
                disk_usage=disk.percent,
                disk_used=disk.used,
                disk_total=disk.total,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                load_average=list(load_avg),
                timestamp=datetime.now()
            )
        except Exception as e:
            structured_logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used=0,
                memory_total=0,
                disk_usage=0.0,
                disk_used=0,
                disk_total=0,
                network_bytes_sent=0,
                network_bytes_recv=0,
                load_average=[0.0, 0.0, 0.0],
                timestamp=datetime.now()
            )

    def collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        try:
            # 计算平均响应时间
            avg_response_time = (
                sum(self.request_times) / len(self.request_times)
                if self.request_times else 0
            )

            # 计算P95和P99响应时间
            sorted_times = sorted(self.request_times)
            p95_response_time = self._calculate_percentile(sorted_times, 95)
            p99_response_time = self._calculate_percentile(sorted_times, 99)

            # 计算错误率
            error_rate = (
                self.error_count / self.request_count
                if self.request_count > 0 else 0
            )

            # 计算缓存命中率
            total_cache_requests = self.cache_hits + self.cache_misses
            cache_hit_rate = (
                self.cache_hits / total_cache_requests
                if total_cache_requests > 0 else 0
            )

            return ApplicationMetrics(
                request_count=self.request_count,
                response_time_avg=avg_response_time,
                response_time_p95=p95_response_time,
                response_time_p99=p99_response_time,
                error_rate=error_rate,
                active_connections=0,  # 需要从连接池获取
                cache_hit_rate=cache_hit_rate,
                db_connection_pool_size=0,  # 需要从数据库管理器获取
                db_connection_pool_used=0,  # 需要从数据库管理器获取
                timestamp=datetime.now()
            )
        except Exception as e:
            structured_logger.error(f"收集应用指标失败: {e}")
            return ApplicationMetrics(
                request_count=0,
                response_time_avg=0.0,
                response_time_p95=0.0,
                response_time_p99=0.0,
                error_rate=0.0,
                active_connections=0,
                cache_hit_rate=0.0,
                db_connection_pool_size=0,
                db_connection_pool_used=0,
                timestamp=datetime.now()
            )

    def _calculate_percentile(self, sorted_values: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not sorted_values:
            return 0.0
        
        index = int((percentile / 100.0) * len(sorted_values))
        if index >= len(sorted_values):
            index = len(sorted_values) - 1
        
        return sorted_values[index]

    def record_request(self, response_time: float, is_error: bool = False, endpoint: str = None):
        """记录请求指标"""
        self.request_count += 1
        self.request_times.append(response_time)

        if is_error:
            self.error_count += 1

        # 记录端点指标
        if endpoint:
            metrics = self.endpoint_metrics[endpoint]
            metrics['count'] += 1
            metrics['total_time'] += response_time
            metrics['response_times'].append(response_time)
            if is_error:
                metrics['error_count'] += 1

    def record_cache_hit(self, hit: bool):
        """记录缓存命中"""
        if hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1

    def get_endpoint_metrics(self, endpoint: str) -> Dict[str, Any]:
        """获取端点指标"""
        metrics = self.endpoint_metrics.get(endpoint, {})
        if not metrics:
            return {}

        response_times = list(metrics['response_times'])
        avg_response_time = metrics['total_time'] / metrics['count'] if metrics['count'] > 0 else 0
        error_rate = metrics['error_count'] / metrics['count'] if metrics['count'] > 0 else 0

        return {
            'endpoint': endpoint,
            'request_count': metrics['count'],
            'avg_response_time': avg_response_time,
            'error_rate': error_rate,
            'p95_response_time': self._calculate_percentile(sorted(response_times), 95),
            'p99_response_time': self._calculate_percentile(sorted(response_times), 99)
        }

    def get_all_endpoint_metrics(self) -> List[Dict[str, Any]]:
        """获取所有端点指标"""
        return [
            self.get_endpoint_metrics(endpoint) 
            for endpoint in self.endpoint_metrics.keys()
        ]

    def reset(self):
        """重置指标"""
        self.request_times.clear()
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.endpoint_metrics.clear()


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage': 90.0,
            'response_time_avg': 1.0,
            'error_rate': 0.05
        }
        self.monitoring_enabled = True

    def set_alert_threshold(self, metric: str, threshold: float):
        """设置告警阈值"""
        self.alert_thresholds[metric] = threshold

    async def check_alerts(self) -> List[Dict[str, Any]]:
        """检查告警"""
        if not self.monitoring_enabled:
            return []

        alerts = []

        try:
            # 检查系统指标
            system_metrics = self.metrics_collector.collect_system_metrics()
            
            if system_metrics.cpu_percent > self.alert_thresholds.get('cpu_percent', 80):
                alerts.append({
                    'type': 'system',
                    'metric': 'cpu_percent',
                    'value': system_metrics.cpu_percent,
                    'threshold': self.alert_thresholds['cpu_percent'],
                    'message': f'CPU使用率过高: {system_metrics.cpu_percent:.1f}%'
                })

            if system_metrics.memory_percent > self.alert_thresholds.get('memory_percent', 85):
                alerts.append({
                    'type': 'system',
                    'metric': 'memory_percent',
                    'value': system_metrics.memory_percent,
                    'threshold': self.alert_thresholds['memory_percent'],
                    'message': f'内存使用率过高: {system_metrics.memory_percent:.1f}%'
                })

            if system_metrics.disk_usage > self.alert_thresholds.get('disk_usage', 90):
                alerts.append({
                    'type': 'system',
                    'metric': 'disk_usage',
                    'value': system_metrics.disk_usage,
                    'threshold': self.alert_thresholds['disk_usage'],
                    'message': f'磁盘使用率过高: {system_metrics.disk_usage:.1f}%'
                })

            # 检查应用指标
            app_metrics = self.metrics_collector.collect_application_metrics()
            
            if app_metrics.response_time_avg > self.alert_thresholds.get('response_time_avg', 1.0):
                alerts.append({
                    'type': 'application',
                    'metric': 'response_time_avg',
                    'value': app_metrics.response_time_avg,
                    'threshold': self.alert_thresholds['response_time_avg'],
                    'message': f'平均响应时间过长: {app_metrics.response_time_avg:.3f}s'
                })

            if app_metrics.error_rate > self.alert_thresholds.get('error_rate', 0.05):
                alerts.append({
                    'type': 'application',
                    'metric': 'error_rate',
                    'value': app_metrics.error_rate,
                    'threshold': self.alert_thresholds['error_rate'],
                    'message': f'错误率过高: {app_metrics.error_rate:.2%}'
                })

            # 记录告警
            for alert in alerts:
                structured_logger.warning(
                    f"性能告警: {alert['message']}",
                    alert_type=alert['type'],
                    metric=alert['metric'],
                    value=alert['value'],
                    threshold=alert['threshold']
                )

        except Exception as e:
            structured_logger.error(f"检查告警失败: {e}")

        return alerts

    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        structured_logger.info("性能监控已启用")

    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        structured_logger.info("性能监控已禁用")


# 全局指标收集器实例
metrics_collector = MetricsCollector()
performance_monitor = PerformanceMonitor(metrics_collector)
