version: '3.8'

services:
  # FastAPI应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: mxtt-fastapi
    restart: unless-stopped
    ports:
      - "9099:9099"
    environment:
      - APP_ENVIRONMENT=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=mxtt_user
      - DB_PASSWORD=mxtt_password
      - DB_DATABASE=mxtt_production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
      - ./.env.production:/app/.env.production
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mxtt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9099/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mxtt-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: mxtt_production
      MYSQL_USER: mxtt_user
      MYSQL_PASSWORD: mxtt_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./scripts/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mxtt-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: mxtt-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_password --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./scripts/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - mxtt-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: mxtt-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./scripts/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./scripts/nginx/conf.d:/etc/nginx/conf.d
      - ./scripts/nginx/ssl:/etc/nginx/ssl
      - ./storage/uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - mxtt-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: mxtt-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./scripts/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mxtt-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: mxtt-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./scripts/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - mxtt-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mxtt-network:
    driver: bridge
