"""
认证控制器模块
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from core.logging.structured_logger import log_api_call
from core.exceptions import AuthenticationException, BusinessException, ErrorCode

from ..services.auth import AuthService, security
from ..schemas.auth import (
    LoginSchema, TokenSchema, RefreshTokenSchema, CaptchaSchema,
    UserInfoSchema, PasswordChangeSchema
)
from ..schemas.user import CurrentUserSchema


class AuthController:
    """认证控制器"""

    def __init__(self, auth_service: AuthService):
        self.auth_service = auth_service

    @log_api_call("用户登录")
    async def login(self, login_data: LoginSchema, request: Request) -> Dict[str, Any]:
        """用户登录"""
        try:
            token_data = await self.auth_service.login(login_data, request)
            
            return {
                "success": True,
                "message": "登录成功",
                "data": {
                    "access_token": token_data.access_token,
                    "refresh_token": token_data.refresh_token,
                    "token_type": token_data.token_type,
                    "expires_in": token_data.expires_in
                }
            }

        except AuthenticationException as e:
            return {
                "success": False,
                "message": e.detail,
                "code": e.error_code.code
            }
        except Exception as e:
            return {
                "success": False,
                "message": "登录失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("用户登出")
    async def logout(
        self, 
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> Dict[str, Any]:
        """用户登出"""
        try:
            token = credentials.credentials
            success = await self.auth_service.logout(token)
            
            if success:
                return {
                    "success": True,
                    "message": "登出成功"
                }
            else:
                return {
                    "success": False,
                    "message": "登出失败"
                }

        except Exception as e:
            return {
                "success": False,
                "message": "登出失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("刷新令牌")
    async def refresh_token(self, refresh_data: RefreshTokenSchema) -> Dict[str, Any]:
        """刷新访问令牌"""
        try:
            token_data = await self.auth_service.refresh_token(refresh_data.refresh_token)
            
            return {
                "success": True,
                "message": "令牌刷新成功",
                "data": {
                    "access_token": token_data.access_token,
                    "token_type": token_data.token_type,
                    "expires_in": token_data.expires_in
                }
            }

        except AuthenticationException as e:
            return {
                "success": False,
                "message": e.detail,
                "code": e.error_code.code
            }
        except Exception as e:
            return {
                "success": False,
                "message": "令牌刷新失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("获取用户信息")
    async def get_user_info(
        self, 
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ) -> Dict[str, Any]:
        """获取当前用户信息"""
        try:
            user_info = await self.auth_service.get_user_info(current_user.user_id)
            
            return {
                "success": True,
                "data": {
                    "user": user_info.model_dump(),
                    "roles": user_info.roles,
                    "permissions": user_info.permissions
                }
            }

        except BusinessException as e:
            return {
                "success": False,
                "message": e.detail,
                "code": e.error_code.code
            }
        except Exception as e:
            return {
                "success": False,
                "message": "获取用户信息失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("修改密码")
    async def change_password(
        self, 
        password_data: PasswordChangeSchema,
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ) -> Dict[str, Any]:
        """修改当前用户密码"""
        try:
            success = await self.auth_service.change_password(current_user.user_id, password_data)
            
            if success:
                return {
                    "success": True,
                    "message": "密码修改成功，请重新登录"
                }
            else:
                return {
                    "success": False,
                    "message": "密码修改失败"
                }

        except BusinessException as e:
            return {
                "success": False,
                "message": e.detail,
                "code": e.error_code.code
            }
        except Exception as e:
            return {
                "success": False,
                "message": "密码修改失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("获取验证码")
    async def get_captcha(self) -> Dict[str, Any]:
        """获取验证码"""
        try:
            captcha_data = await self.auth_service.generate_captcha()
            
            return {
                "success": True,
                "data": {
                    "uuid": captcha_data.uuid,
                    "img": captcha_data.img,
                    "captcha_enabled": captcha_data.captcha_enabled
                }
            }

        except BusinessException as e:
            return {
                "success": False,
                "message": e.detail,
                "code": e.error_code.code
            }
        except Exception as e:
            return {
                "success": False,
                "message": "获取验证码失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }

    @log_api_call("获取路由信息")
    async def get_routers(
        self, 
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ) -> Dict[str, Any]:
        """获取用户路由信息"""
        try:
            # 这里需要根据用户权限返回对应的路由信息
            # 暂时返回示例数据
            routers = [
                {
                    "name": "System",
                    "path": "/system",
                    "hidden": False,
                    "redirect": "noRedirect",
                    "component": "Layout",
                    "alwaysShow": True,
                    "meta": {
                        "title": "系统管理",
                        "icon": "system",
                        "noCache": False,
                        "link": None
                    },
                    "children": [
                        {
                            "name": "User",
                            "path": "user",
                            "hidden": False,
                            "component": "system/user/index",
                            "meta": {
                                "title": "用户管理",
                                "icon": "user",
                                "noCache": False,
                                "link": None
                            }
                        }
                    ]
                }
            ]
            
            return {
                "success": True,
                "data": routers
            }

        except Exception as e:
            return {
                "success": False,
                "message": "获取路由信息失败",
                "code": ErrorCode.SYSTEM_ERROR.code
            }


def create_auth_router() -> APIRouter:
    """创建认证路由器"""
    router = APIRouter(tags=["认证管理"])
    
    # 依赖注入
    def get_auth_controller(
        auth_service: AuthService = Depends()
    ) -> AuthController:
        return AuthController(auth_service)

    # 注册路由
    @router.post("/login", summary="用户登录")
    async def login(
        login_data: LoginSchema,
        request: Request,
        controller: AuthController = Depends(get_auth_controller)
    ):
        return await controller.login(login_data, request)

    @router.post("/logout", summary="用户登出")
    async def logout(
        controller: AuthController = Depends(get_auth_controller),
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ):
        return await controller.logout(credentials)

    @router.post("/refresh", summary="刷新令牌")
    async def refresh_token(
        refresh_data: RefreshTokenSchema,
        controller: AuthController = Depends(get_auth_controller)
    ):
        return await controller.refresh_token(refresh_data)

    @router.get("/getInfo", summary="获取用户信息")
    async def get_user_info(
        controller: AuthController = Depends(get_auth_controller),
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ):
        return await controller.get_user_info(current_user)

    @router.put("/profile/password", summary="修改密码")
    async def change_password(
        password_data: PasswordChangeSchema,
        controller: AuthController = Depends(get_auth_controller),
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ):
        return await controller.change_password(password_data, current_user)

    @router.get("/captchaImage", summary="获取验证码")
    async def get_captcha(
        controller: AuthController = Depends(get_auth_controller)
    ):
        return await controller.get_captcha()

    @router.get("/getRouters", summary="获取路由信息")
    async def get_routers(
        controller: AuthController = Depends(get_auth_controller),
        current_user: CurrentUserSchema = Depends(AuthService.get_current_user)
    ):
        return await controller.get_routers(current_user)

    return router
