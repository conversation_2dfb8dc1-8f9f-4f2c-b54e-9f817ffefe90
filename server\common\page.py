"""
分页服务模块
"""
import math
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from sqlalchemy import func, select, Select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from .common import CamelCaseService


class PageResponseModel(BaseModel):
    """
    列表分页查询返回模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    rows: List = []
    page_num: Optional[int] = None
    page_size: Optional[int] = None
    total: int
    has_next: Optional[bool] = None


class PaginationService:
    """
    分页工具类
    """

    @classmethod
    def get_page_obj(cls, data_list: List, page_num: int, page_size: int):
        """
        输入数据列表data_list和分页信息，返回分页数据列表结果

        :param data_list: 原始数据列表
        :param page_num: 当前页码
        :param page_size: 当前页面数据量
        :return: 分页数据对象
        """
        # 计算起始索引和结束索引
        start = (page_num - 1) * page_size
        end = start + page_size  # 更清晰的赋值方式

        # 根据计算得到的起始索引和结束索引对数据列表进行切片
        paginated_data = data_list[start:end]

        # 判断是否还有下一页
        total_pages = math.ceil(len(data_list) / page_size)
        has_next = total_pages > page_num if total_pages else False

        return PageResponseModel(
            rows=paginated_data,
            pageNum=page_num,
            pageSize=page_size,
            total=len(data_list),
            hasNext=has_next,
        )

    @classmethod
    async def paginate(cls, db: AsyncSession, query: Select, page_num: int, page_size: int, is_page: bool = False):
        """
        输入查询语句和分页信息，返回分页数据列表结果

        :param db: orm对象
        :param query: sqlalchemy查询语句
        :param page_num: 当前页码
        :param page_size: 当前页面数据量
        :param is_page: 是否开启分页
        :return: 分页数据对象
        """
        if is_page:
            # 执行分页查询
            total_query = select(func.count('*')).select_from(query.subquery())
            total = (await db.execute(total_query)).scalar()
            paginated_query = query.offset((page_num - 1) * page_size).limit(page_size)
            query_result = await db.execute(paginated_query)
            
            # 处理查询结果
            paginated_data = [row[0] if len(row) == 1 else row for row in query_result]
            
            # 判断是否还有下一页
            total_pages = math.ceil(total / page_size) if total else 0
            has_next = total_pages > page_num if total_pages else False
            
            return PageResponseModel(
                rows=CamelCaseService.transform_result(paginated_data),
                pageNum=page_num,
                pageSize=page_size,
                total=total,
                hasNext=has_next,
            )
        else:
            # 不分页时直接执行查询并处理结果
            query_result = await db.execute(query)
            no_paginated_data = [row[0] if len(row) == 1 else row for row in query_result]
            
            return CamelCaseService.transform_result(no_paginated_data)

# 删除重复的分页函数，使用类方法版本
