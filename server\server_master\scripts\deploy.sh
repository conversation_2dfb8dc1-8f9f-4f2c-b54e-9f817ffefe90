#!/bin/bash

# MXTT-FastAPI 部署脚本
# 使用方法: ./scripts/deploy.sh [dev|prod] [--build]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "MXTT-FastAPI 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境部署"
    echo "  prod    生产环境部署"
    echo ""
    echo "选项:"
    echo "  --build     强制重新构建镜像"
    echo "  --clean     清理旧的容器和镜像"
    echo "  --logs      显示容器日志"
    echo "  --stop      停止所有服务"
    echo "  --restart   重启所有服务"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev              # 启动开发环境"
    echo "  $0 prod --build     # 重新构建并启动生产环境"
    echo "  $0 dev --logs       # 查看开发环境日志"
    echo "  $0 prod --stop      # 停止生产环境"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p storage/uploads
    mkdir -p storage/downloads
    mkdir -p storage/generated
    mkdir -p assets/font
    mkdir -p scripts/mysql
    mkdir -p scripts/redis
    mkdir -p scripts/nginx/conf.d
    mkdir -p scripts/nginx/ssl
    mkdir -p scripts/prometheus
    mkdir -p scripts/grafana/provisioning
    
    log_info "目录创建完成"
}

# 检查环境配置文件
check_env_files() {
    local env=$1
    log_info "检查环境配置文件..."
    
    if [ "$env" = "dev" ]; then
        if [ ! -f ".env.development" ]; then
            log_warn ".env.development 文件不存在，将使用默认配置"
        fi
    elif [ "$env" = "prod" ]; then
        if [ ! -f ".env.production" ]; then
            log_error ".env.production 文件不存在，请创建生产环境配置文件"
            exit 1
        fi
    fi
}

# 部署开发环境
deploy_dev() {
    local build_flag=$1
    log_info "部署开发环境..."
    
    check_env_files "dev"
    create_directories
    
    if [ "$build_flag" = "--build" ]; then
        log_info "重新构建开发环境镜像..."
        docker-compose -f docker-compose.dev.yml build --no-cache
    fi
    
    log_info "启动开发环境服务..."
    docker-compose -f docker-compose.dev.yml up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services "dev"
    
    log_info "开发环境部署完成!"
    log_info "应用地址: http://localhost:9099"
    log_info "API文档: http://localhost:9099/docs"
    log_info "数据库管理: http://localhost:8080"
    log_info "Redis管理: http://localhost:8081"
}

# 部署生产环境
deploy_prod() {
    local build_flag=$1
    log_info "部署生产环境..."
    
    check_env_files "prod"
    create_directories
    
    if [ "$build_flag" = "--build" ]; then
        log_info "重新构建生产环境镜像..."
        docker-compose build --no-cache
    fi
    
    log_info "启动生产环境服务..."
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 20
    
    # 检查服务状态
    check_services "prod"
    
    log_info "生产环境部署完成!"
    log_info "应用地址: http://localhost:9099"
    log_info "Nginx代理: http://localhost"
    log_info "监控面板: http://localhost:3000"
    log_info "指标收集: http://localhost:9090"
}

# 检查服务状态
check_services() {
    local env=$1
    log_info "检查服务状态..."
    
    if [ "$env" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml ps
    else
        docker-compose ps
    fi
    
    # 检查应用健康状态
    log_info "检查应用健康状态..."
    for i in {1..30}; do
        if curl -f http://localhost:9099/health &> /dev/null; then
            log_info "应用健康检查通过"
            break
        else
            if [ $i -eq 30 ]; then
                log_warn "应用健康检查失败，请检查日志"
            else
                log_debug "等待应用启动... ($i/30)"
                sleep 2
            fi
        fi
    done
}

# 显示日志
show_logs() {
    local env=$1
    log_info "显示容器日志..."
    
    if [ "$env" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose logs -f
    fi
}

# 停止服务
stop_services() {
    local env=$1
    log_info "停止服务..."
    
    if [ "$env" = "dev" ]; then
        docker-compose -f docker-compose.dev.yml down
    else
        docker-compose down
    fi
    
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    local env=$1
    log_info "重启服务..."
    
    stop_services $env
    sleep 5
    
    if [ "$env" = "dev" ]; then
        deploy_dev
    else
        deploy_prod
    fi
}

# 清理资源
clean_resources() {
    log_warn "清理Docker资源..."
    
    # 停止所有容器
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    log_info "资源清理完成"
}

# 主函数
main() {
    local env=$1
    local option=$2
    
    # 检查参数
    if [ -z "$env" ]; then
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    case "$option" in
        --help)
            show_help
            ;;
        --logs)
            show_logs $env
            ;;
        --stop)
            stop_services $env
            ;;
        --restart)
            restart_services $env
            ;;
        --clean)
            clean_resources
            ;;
        --build|"")
            case "$env" in
                dev)
                    deploy_dev $option
                    ;;
                prod)
                    deploy_prod $option
                    ;;
                *)
                    log_error "未知环境: $env"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        *)
            log_error "未知选项: $option"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
